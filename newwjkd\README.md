# 文件快递服务 - uni-app版本

这是一个基于uni-app x (Vue3)开发的文件快递服务应用，从原iApp v3项目转换而来。

## 项目简介

文件快递服务是一个简单易用的文件传输工具，支持：
- 文件上传和下载
- 取件码分享
- 文件管理
- 上传记录查看
- 捐赠支持

## 技术栈

- **框架**: uni-app x (Vue3)
- **语言**: JavaScript/TypeScript
- **样式**: CSS3 + CSS变量
- **API**: RESTful API (https://file.8845.top)

## 项目结构

```
newwjkd/
├── pages/                  # 页面文件
│   ├── splash/            # 启动页
│   ├── loading/           # 加载页
│   ├── index/             # 主页
│   ├── fileManager/       # 文件管理器
│   ├── uploadRecord/      # 上传记录
│   └── donate/            # 捐赠页面
├── components/            # 公共组件
│   ├── LoadingModal.uvue  # 加载模态框
│   └── ConfirmDialog.uvue # 确认对话框
├── utils/                 # 工具函数
│   ├── api.js            # API接口
│   ├── storage.js        # 本地存储
│   ├── common.js         # 通用工具
│   └── theme.js          # 主题配置
├── static/               # 静态资源
│   ├── css/              # 全局样式
│   └── images/           # 图片资源
├── App.uvue              # 应用入口
├── pages.json            # 页面配置
└── manifest.json         # 应用配置
```

## 主要功能

### 1. 文件上传
- 支持系统文件管理器选择
- 支持自制文件管理器浏览
- 上传进度显示
- 生成取件码和二维码

### 2. 文件下载
- 输入8位取件码
- 下载进度显示
- 自动保存到本地

### 3. 文件管理
- 浏览设备文件系统
- 支持多种文件类型
- 文件信息显示

### 4. 上传记录
- 查看历史上传记录
- 复制取件码和链接
- 删除和清空记录

### 5. 捐赠支持
- 支付宝和微信支付
- 二维码扫码支付
- 捐赠记录展示

## 主题配置

项目使用CSS变量实现主题系统，主色调为 `#1f999b`（青绿色）。

主要颜色：
- 主色：`#1f999b`
- 主色深：`#16a085`
- 辅助色：`#f98b8b`
- 强调色：`#ff6b6b`

## API接口

基础URL: `https://file.8845.top`

主要接口：
- `POST /api/upfile.php` - 文件上传
- `POST /api/getfile.php` - 获取文件信息
- `GET /api/get_update.php` - 检查更新
- `GET /api/get_gg.php` - 获取公告

## 开发说明

### 环境要求
- HBuilderX 3.8+
- uni-app x 支持
- Vue 3

### 运行项目
1. 使用HBuilderX打开项目
2. 选择运行到相应平台
3. 或使用命令行工具构建

### 注意事项
1. 文件管理器功能需要原生API支持
2. 部分功能在不同平台可能有差异
3. 需要配置相应的权限申请

## 从iApp v3转换说明

本项目从iApp v3 (裕语言3.0)转换而来，主要转换内容：

1. **语法转换**：
   - `.iyu`文件 → `.uvue`文件
   - iApp语法 → Vue3语法
   - 事件处理转换

2. **功能适配**：
   - 文件系统API适配
   - 网络请求适配
   - 本地存储适配

3. **UI重构**：
   - 布局系统重构
   - 样式系统重构
   - 组件化改造

## 版本信息

- 当前版本：2.1.0
- 基于原iApp v3项目
- 转换完成时间：2025年

## 许可证

本项目仅供学习和参考使用。

## 联系方式

如有问题或建议，请通过应用内捐赠页面联系开发者。
