// 本地存储工具
const STORAGE_KEYS = {
  UPLOAD_RECORDS: 'upload_records',
  APP_VERSION: 'app_version',
  NO_AD: 'no_ad'
}

// 获取上传记录
export const getUploadRecords = () => {
  try {
    const records = uni.getStorageSync(STORAGE_KEYS.UPLOAD_RECORDS)
    return records ? JSON.parse(records) : []
  } catch (e) {
    return []
  }
}

// 保存上传记录
export const saveUploadRecord = (record) => {
  try {
    const records = getUploadRecords()
    const newRecord = {
      id: Date.now(),
      filename: record.filename,
      gcode: record.gcode,
      uploadTime: new Date().toLocaleString(),
      ...record
    }
    records.unshift(newRecord)
    
    // 只保留最近100条记录
    if (records.length > 100) {
      records.splice(100)
    }
    
    uni.setStorageSync(STORAGE_KEYS.UPLOAD_RECORDS, JSON.stringify(records))
    return newRecord
  } catch (e) {
    console.error('保存上传记录失败:', e)
    return null
  }
}

// 删除上传记录
export const deleteUploadRecord = (id) => {
  try {
    const records = getUploadRecords()
    const filteredRecords = records.filter(record => record.id !== id)
    uni.setStorageSync(STORAGE_KEYS.UPLOAD_RECORDS, JSON.stringify(filteredRecords))
    return true
  } catch (e) {
    console.error('删除上传记录失败:', e)
    return false
  }
}

// 清空上传记录
export const clearUploadRecords = () => {
  try {
    uni.setStorageSync(STORAGE_KEYS.UPLOAD_RECORDS, JSON.stringify([]))
    return true
  } catch (e) {
    console.error('清空上传记录失败:', e)
    return false
  }
}

// 获取应用版本
export const getAppVersion = () => {
  try {
    return uni.getStorageSync(STORAGE_KEYS.APP_VERSION) || '2.1'
  } catch (e) {
    return '2.1'
  }
}

// 设置应用版本
export const setAppVersion = (version) => {
  try {
    uni.setStorageSync(STORAGE_KEYS.APP_VERSION, version)
    return true
  } catch (e) {
    return false
  }
}

// 获取免广告状态
export const getNoAdStatus = () => {
  try {
    return uni.getStorageSync(STORAGE_KEYS.NO_AD) === 'true'
  } catch (e) {
    return false
  }
}

// 设置免广告状态
export const setNoAdStatus = (status) => {
  try {
    uni.setStorageSync(STORAGE_KEYS.NO_AD, status ? 'true' : 'false')
    return true
  } catch (e) {
    return false
  }
}
