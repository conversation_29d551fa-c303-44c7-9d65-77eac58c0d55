// 主题配置文件
// 对应原项目中的着色.myu功能

// 主题色彩配置
export const themeColors = {
  // 主色调
  primary: '#1f999b',
  primaryDark: '#16a085',
  primaryLight: '#4db6ac',
  
  // 辅助色
  secondary: '#f98b8b',
  accent: '#ff6b6b',
  
  // 状态色
  success: '#4caf50',
  warning: '#ff9800',
  error: '#f44336',
  info: '#2196f3',
  
  // 中性色
  white: '#ffffff',
  black: '#000000',
  gray: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#eeeeee',
    300: '#e0e0e0',
    400: '#bdbdbd',
    500: '#9e9e9e',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121'
  },
  
  // 文本色
  text: {
    primary: '#333333',
    secondary: '#666666',
    disabled: '#999999',
    hint: '#cccccc'
  },
  
  // 背景色
  background: {
    default: '#ffffff',
    paper: '#f8f8f8',
    disabled: '#f0f0f0'
  },
  
  // 边框色
  border: {
    light: '#f0f0f0',
    default: '#e0e0e0',
    dark: '#cccccc'
  }
}

// 渐变色配置
export const gradients = {
  primary: `linear-gradient(135deg, ${themeColors.primary} 0%, ${themeColors.primaryDark} 100%)`,
  secondary: `linear-gradient(135deg, ${themeColors.secondary} 0%, ${themeColors.accent} 100%)`,
  success: `linear-gradient(135deg, ${themeColors.success} 0%, #66bb6a 100%)`,
  warning: `linear-gradient(135deg, ${themeColors.warning} 0%, #ffb74d 100%)`,
  error: `linear-gradient(135deg, ${themeColors.error} 0%, #ef5350 100%)`
}

// 阴影配置
export const shadows = {
  none: 'none',
  small: '0 2px 4px rgba(0, 0, 0, 0.1)',
  medium: '0 4px 8px rgba(0, 0, 0, 0.1)',
  large: '0 8px 16px rgba(0, 0, 0, 0.1)',
  xlarge: '0 12px 24px rgba(0, 0, 0, 0.15)'
}

// 圆角配置
export const borderRadius = {
  none: '0',
  small: '4px',
  medium: '8px',
  large: '12px',
  xlarge: '16px',
  round: '50%'
}

// 字体配置
export const typography = {
  fontFamily: {
    default: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    mono: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, Courier, monospace'
  },
  fontSize: {
    xs: '10px',
    sm: '12px',
    base: '14px',
    lg: '16px',
    xl: '18px',
    '2xl': '20px',
    '3xl': '24px',
    '4xl': '28px',
    '5xl': '32px'
  },
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800'
  },
  lineHeight: {
    tight: '1.2',
    normal: '1.4',
    relaxed: '1.6',
    loose: '1.8'
  }
}

// 间距配置
export const spacing = {
  0: '0',
  1: '4px',
  2: '8px',
  3: '12px',
  4: '16px',
  5: '20px',
  6: '24px',
  8: '32px',
  10: '40px',
  12: '48px',
  16: '64px',
  20: '80px',
  24: '96px'
}

// 动画配置
export const animations = {
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms'
  },
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out'
  }
}

// 断点配置
export const breakpoints = {
  xs: '320px',
  sm: '375px',
  md: '768px',
  lg: '1024px',
  xl: '1200px'
}

// 获取主题变量的工具函数
export const getThemeVar = (path) => {
  const keys = path.split('.')
  let value = { themeColors, gradients, shadows, borderRadius, typography, spacing, animations, breakpoints }
  
  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key]
    } else {
      return undefined
    }
  }
  
  return value
}

// 生成CSS变量
export const generateCSSVars = () => {
  const vars = {}
  
  // 颜色变量
  Object.entries(themeColors).forEach(([key, value]) => {
    if (typeof value === 'string') {
      vars[`--color-${key}`] = value
    } else if (typeof value === 'object') {
      Object.entries(value).forEach(([subKey, subValue]) => {
        vars[`--color-${key}-${subKey}`] = subValue
      })
    }
  })
  
  // 间距变量
  Object.entries(spacing).forEach(([key, value]) => {
    vars[`--spacing-${key}`] = value
  })
  
  // 字体变量
  Object.entries(typography.fontSize).forEach(([key, value]) => {
    vars[`--font-size-${key}`] = value
  })
  
  return vars
}

// 应用主题到页面
export const applyTheme = () => {
  const cssVars = generateCSSVars()
  const root = document.documentElement || document.body
  
  Object.entries(cssVars).forEach(([key, value]) => {
    root.style.setProperty(key, value)
  })
}

// 切换主题模式（预留功能）
export const toggleThemeMode = (mode = 'light') => {
  // 这里可以实现深色模式切换
  console.log('切换主题模式:', mode)
}

// 获取当前主题模式
export const getCurrentThemeMode = () => {
  // 从本地存储获取主题模式
  return uni.getStorageSync('theme-mode') || 'light'
}

// 保存主题模式
export const saveThemeMode = (mode) => {
  uni.setStorageSync('theme-mode', mode)
}
