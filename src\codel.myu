fn 编码(原文)
java(字节数组,原文,"java.lang.String.getBytes")
//转成byte[]字节数组
java(sss.密文,null,"android.util.Base64.encodeToString","byte[]",字节数组,"int",0)
sr(sss.密文,"
","",sss.密文)
end fn

fn 解码(密文)
java(字节数组,null,"android.util.Base64.decode","String",密文,"int",0)
//Base64解码，0是模式
javanew(sss.原文,"java.lang.String","byte[]",字节数组)
//由于返回的是byte[]字节数组，所以转化成字符串
end fn

fn 混淆加密(原文)
s bl="1.2.3.4.5.6.7.8.9.0.q.w.e.r.t.y.u.i.o.p.a.s.d.f.g.h.j.k.l.z.x.c.v.b.n.m.Q.W.E.R.T.Y.U.I.O.P.A.S.D.F.G.H.J.K.L.Z.X.C.V.B.N.M"
s tc="ア.イ.ウ.エ.オ.ァ.ィ.ゥ.ェ.ォ.カ.キ.ク.ケ.コ.ガ.ギ.グ.ゲ.ゴ.サ.シ.ス.セ.ソ.ザ.ジ.ズ.ゼ.ゾ.タ.チ.ツ.テ.ト.ダ.ヂ.ヅ.デ.ド.ッ.ナ.ニ.ヌ.ネ.ノ.ハ.ヒ.フ.ヘ.ホ.バ.ビ.ブ.ベ.ボ.パ.ピ.プ.ペ.ポ.マ"
sl(bl,".",sz1)
sl(tc,".",sz2)
s a=0
w(a&lt;61)
{
  s+(1,a)
  sgsz(sz1,a,zf1)
  sgsz(sz2,a,zf2)
  sr(原文,zf1,zf2,原文)
}
sss 密文=原文
end fn

fn 混淆解密(密文)
s bl="1.2.3.4.5.6.7.8.9.0.q.w.e.r.t.y.u.i.o.p.a.s.d.f.g.h.j.k.l.z.x.c.v.b.n.m.Q.W.E.R.T.Y.U.I.O.P.A.S.D.F.G.H.J.K.L.Z.X.C.V.B.N.M"
s tc="ア.イ.ウ.エ.オ.ァ.ィ.ゥ.ェ.ォ.カ.キ.ク.ケ.コ.ガ.ギ.グ.ゲ.ゴ.サ.シ.ス.セ.ソ.ザ.ジ.ズ.ゼ.ゾ.タ.チ.ツ.テ.ト.ダ.ヂ.ヅ.デ.ド.ッ.ナ.ニ.ヌ.ネ.ノ.ハ.ヒ.フ.ヘ.ホ.バ.ビ.ブ.ベ.ボ.パ.ピ.プ.ペ.ポ.マ"
sl(bl,".",sz1)
sl(tc,".",sz2)
s a=0
w(a&lt;61)
{
  s+(1,a)
  sgsz(sz1,a,zf1)
  sgsz(sz2,a,zf2)
  sr(密文,zf2,zf1,密文)
}
sss 原文=密文
end fn



////以下是你们要调用的

fn 加密(nr)
fn codel.编码(nr)
syso(sss.密文)
fn codel.混淆加密(sss.密文)
syso(sss.密文)
//最后的加密结果存储在  sss.密文
end fn

fn 解密(nr)
fn codel.混淆解密(nr)
syso(sss.原文)
fn codel.解码(sss.原文)
syso(sss.原文)
//最后的解密结果存储在  sss.原文
end fn