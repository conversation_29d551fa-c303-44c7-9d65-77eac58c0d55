<View id="1" did="0" type="RelativeLayout">
<ppt>width=-1
height=-1
BackgroundColor=#ffffff</ppt>
<event></event>
</View>
<View id="9" did="1" type="NestedScrollView">
<ppt>width=-1
height=-1
background=white</ppt>
<event></event>
</View>
<View id="10" did="9" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical</ppt>
<event></event>
</View>
<View id="7" did="10" type="LinearLayout">
<ppt>width=-1
height=-2
orientation=vertical
gravity=center</ppt>
<event></event>
</View>
<View id="8" did="7" type="ImageView">
<ppt>width=-1
height=-1
src=@3.png</ppt>
<event></event>
</View>
<View id="11" did="7" type="LinearLayout">
<ppt>width=-1
height=-2
orientation=vertical
gravity=center</ppt>
<event></event>
</View>
<View id="12" did="11" type="TextView">
<ppt>width=-2
height=-2
text=文件快递服务
textColor=#ff1f999b
textStyle=bold
textSize=20sp</ppt>
<event></event>
</View>
<View id="13" did="7" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
layout_marginTop=80dp</ppt>
<event></event>
</View>
<View id="14" did="13" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=80dp
layout_marginRight=80dp
app_CardcornerRadius=10dp
layout_marginBottom=20dp
layout_marginTop=20dp
app_CardElevation=0
</ppt>
<event></event>
</View>
<View id="15" did="14" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
clickable=true
BackgroundRipple=#ffebebeb</ppt>
<event><eventItme type="clicki">fn fun.xs(22)
us(31,"text","")</eventItme></event>
</View>
<View id="17" did="15" type="TextView">
<ppt>width=-2
height=-2
text=取件
textColor=#ff1f999b
textStyle=bold
textSize=15sp
layout_marginTop=12dp
layout_marginBottom=12dp</ppt>
<event></event>
</View>
<View id="18" did="7" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
</ppt>
<event></event>
</View>
<View id="19" did="18" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=80dp
layout_marginRight=80dp
app_CardcornerRadius=10dp
layout_marginBottom=20dp
layout_marginTop=20dp
app_CardElevation=0
</ppt>
<event></event>
</View>
<View id="20" did="19" type="LinearLayout">
<ppt>width=-1
    height=-1
    orientation=vertical
    gravity=center
    clickable=true
    BackgroundRipple=#ffebebeb</ppt>
<event>
        <eventItme type="clicki">
            fn noad.Is_noAd()
            f(sss.noad == false)
            {
                // 直接执行奖励或其他逻辑，跳过广告
                fn fun.xs(36)
            }
            else
            {
                // 直接执行奖励或其他逻辑
                fn fun.xs(36)
            }
        </eventItme>
    </event>
</View>
<View id="21" did="20" type="TextView">
<ppt>width=-2
    height=-2
    text=发件
    textColor=#ffffff
    textStyle=bold
    textSize=15sp
    layout_marginTop=12dp
    layout_marginBottom=12dp</ppt>
<event></event>
</View>
<View id="202" did="7" type="LinearLayout">
<ppt>width=-1
    height=-2
    orientation=horizontal
    layout_marginTop=20dp
    layout_marginBottom=20dp</ppt>
<event></event>
</View>
<View id="212" did="202" type="LinearLayout">
<ppt>width=-1
    height=-1
    orientation=vertical
    gravity=center
    layout_weight=1
    visibility=visible</ppt>
<event>
        <eventItme type="clicki">
            fn fun.xs(255)
        </eventItme>
    </event>
</View>
<View id="213" did="212" type="TextView">
<ppt>width=-2
height=-2
text=捐赠App
textColor=#ffb8b8b8
textStyle=bold
textSize=13sp</ppt>
<event></event>
</View>
<View id="214" did="212" type="LinearLayout">
<ppt>width=50dp
height=1dp
orientation=vertical
layout_marginTop=2dp
background=#ffb8b8b8</ppt>
<event></event>
</View>
<View id="218" did="202" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">fn fun.xs(221)
fn fun.file_jl("$jl")</eventItme></event>
</View>
<View id="219" did="218" type="TextView">
<ppt>width=-2
height=-2
text=上传记录
textColor=#ffb8b8b8
textStyle=bold
textSize=13sp</ppt>
<event></event>
</View>
<View id="220" did="218" type="LinearLayout">
<ppt>width=50dp
height=1dp
orientation=vertical
layout_marginTop=2dp
background=#ffb8b8b8</ppt>
<event></event>
</View>
<View id="22" did="1" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
background=#3b000000
visibility=gone</ppt>
<event><eventItme type="clicki">fn fun.yc(22)
</eventItme></event>
</View>
<View id="23" did="22" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginBottom=20dp
layout_marginRight=20dp
app_CardcornerRadius=10dp
app_CardElevation=0</ppt>
<event></event>
</View>
<View id="24" did="23" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
</ppt>
<event></event>
</View>
<View id="25" did="24" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center</ppt>
<event></event>
</View>
<View id="26" did="25" type="ImageView">
<ppt>width=-2
height=100dp
src=@4.png
layout_marginTop=10dp</ppt>
<event></event>
</View>
<View id="27" did="24" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center</ppt>
<event></event>
</View>
<View id="30" did="27" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginRight=20dp
app_CardcornerRadius=10dp
app_CardElevation=0
app_CardBackgroundColor=#fff8f8f8
layout_marginTop=10dp
layout_marginBottom=10dp</ppt>
<event></event>
</View>
<View id="31" did="30" type="EditText">
<ppt>width=-1
height=-2
text=
hint=请输入8位取件码
textStyle=bold
//textColorHighlight=#ff1f999b
textColorHint=#ffbcbcbc
BackgroundColor=#fff8f8f8
paddingLeft=10dp
paddingRight=10dp
singleLine=true
maxLength=8
inputType=number
gravity=center
textSize=15sp
textColor=#ff1f999b
textCursorDrawable=#ff1f999b</ppt>
<event></event>
</View>
<View id="32" did="24" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center</ppt>
<event></event>
</View>
<View id="33" did="32" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginRight=20dp
app_CardcornerRadius=10dp
app_CardElevation=0
layout_marginTop=10dp
layout_marginBottom=20dp
app_CardBackgroundColor=#ff1f999b</ppt>
<event></event>
</View>
<View id="34" did="33" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
clickable=true
BackgroundRipple=#3b000000</ppt>
<event><eventItme type="clicki">ug(31,"text",qjm)
slg(qjm,k)
f(k&gt;=1)
{
  t()
  {
    
    ufnsui()
    {
      fn fun.yc(22)
      fn fun.xs(170)
      us(177,"text","请稍后")
    }
    stop(1000)
    hs("https://file.8845.top/api/downfile.php","gcode="+qjm,"utf-8",re)
    syso(re)
    f(re==)
    {
      ufnsui()
      {
        fn fun.yc(170)
        tw("连接超时")
      }
    }
    else
    {
      ufnsui()
      {
        json(re,rs)
        json(rs,"get","status",zt)
        json(rs,"get","msg",msg)
        f(zt==1)
        {
          ufnsui()
          {
            json(rs,"get","durl",durl)
            json(rs,"get","wjm",wjm)
            ss(sss.url+durl,xzurl)
            fn fun.yc(170)
            fn fun.xs(178)
            t()
            {
              hdda(xzurl,"/storage/emulated/0/Download/文件快递/",wjm,"","","","",v)
              w(true)
              {
                hddg(v, "downloadpercentage", bfb)
                hddg(v,"status",st)
                f(st=="2")
                {
                  ufnsui()
                  {
                    fn fun.yc(178)
                    tw("下载成功 已保存至 根目录/下载/文件快递/")
                  }
                  break
                }
                ufnsui()
                {
                  us(183,"text",bfb+"%")
                  us(185,"progress",bfb)
                }
              }
            }
          }
        }
        else
        {
          ufnsui()
          {
            fn fun.yc(170)
            tw(msg)
          }
        }
      }
    }
  }
}
else
{
  tw("请输入完整")
}</eventItme></event>
</View>
<View id="35" did="34" type="TextView">
<ppt>width=-2
height=-2
text=获取
textColor=#ffffff
textStyle=bold
textSize=15sp
layout_marginTop=10dp
layout_marginBottom=10dp</ppt>
<event></event>
</View>
<View id="36" did="1" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
background=#3b000000
visibility=gone</ppt>
<event><eventItme type="clicki">fn fun.yc(36)</eventItme></event>
</View>
<View id="37" did="36" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginBottom=20dp
layout_marginRight=20dp
app_CardcornerRadius=10dp
app_CardElevation=0</ppt>
<event></event>
</View>
<View id="38" did="37" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
</ppt>
<event></event>
</View>
<View id="39" did="38" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center</ppt>
<event></event>
</View>
<View id="40" did="39" type="ImageView">
<ppt>width=-2
height=100dp
src=@5.png
layout_marginTop=10dp</ppt>
<event></event>
</View>
<View id="41" did="38" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center</ppt>
<event></event>
</View>
<View id="44" did="38" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center</ppt>
<event></event>
</View>
<View id="45" did="44" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginRight=20dp
app_CardcornerRadius=10dp
app_CardElevation=0
layout_marginTop=10dp
layout_marginBottom=10dp
</ppt>
<event></event>
</View>
<View id="46" did="45" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
clickable=true
BackgroundRipple=#3b000000</ppt>
<event><eventItme type="clicki">t()
{
  ufnsui()
  {
    fn fun.yc(36)
    fn fun.xs(51)
    sss lj="/storage/emulated/0"
    us(72,"text",sss.lj)
    fn fun.file_glq(sss.lj)
  }
}</eventItme></event>
</View>
<View id="47" did="46" type="TextView">
<ppt>width=-2
height=-2
text=自制文件管理器
textColor=#ff1f999b
textStyle=bold
textSize=15sp
layout_marginTop=10dp
layout_marginBottom=10dp</ppt>
<event></event>
</View>
<View id="48" did="44" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginRight=20dp
app_CardcornerRadius=10dp
app_CardElevation=0
layout_marginTop=10dp
layout_marginBottom=20dp
app_CardBackgroundColor=#ff1f999b</ppt>
<event></event>
</View>
<View id="49" did="48" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
clickable=true
BackgroundRipple=#3b000000</ppt>
<event><eventItme type="clicki">fn fun.yc(36)
javags(agc,"android.content.Intent","android.content.Intent.ACTION_GET_CONTENT")
javanew(it,"android.content.Intent","String",agc)

java(c,it,"android.content.Intent.setType","String","*/*")

javags(ca,"android.content.Intent","android.content.Intent.CATEGORY_OPENABLE")
java(c,it,"android.content.Intent.addCategory","String",ca)

java(c,activity,"android.app.Activity.startActivityForResult","android.content.Intent",it,"int",2)</eventItme></event>
</View>
<View id="50" did="49" type="TextView">
<ppt>width=-2
height=-2
text=系统文件管理器
textColor=#ffffff
textStyle=bold
textSize=15sp
layout_marginTop=10dp
layout_marginBottom=10dp</ppt>
<event></event>
</View>
<View id="51" did="1" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
background=#3b000000
visibility=gone</ppt>
<event><eventItme type="clicki">syso("")</eventItme></event>
</View>
<View id="52" did="51" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginBottom=20dp
layout_marginRight=20dp
app_CardcornerRadius=10dp
app_CardElevation=0
layout_marginTop=50dp
layout_marginBottom=50dp</ppt>
<event></event>
</View>
<View id="58" did="52" type="RelativeLayout">
<ppt>width=-1
height=-1</ppt>
<event></event>
</View>
<View id="59" did="58" type="LinearLayout">
<ppt>width=-1
height=-2
orientation=vertical
gravity=center_vertical</ppt>
<event></event>
</View>
<View id="60" did="59" type="TextView">
<ppt>width=-2
height=-2
text=选择文件
textStyle=bold
textSize=17sp
textColor=#ff1f999b
layout_marginTop=20dp
layout_marginLeft=20dp</ppt>
<event></event>
</View>
<View id="72" did="59" type="TextView">
<ppt>width=-2
height=-2
text=/storage/emulated/0
textColor=#ff1f999b
layout_marginLeft=20dp
layout_marginTop=10dp
layout_marginBottom=10dp</ppt>
<event></event>
</View>
<View id="61" did="58" type="ScrollView">
<ppt>width=-1
height=-1
ut_below=59
ut_above=63
layout_marginBottom=10dp</ppt>
<event></event>
</View>
<View id="62" did="61" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical</ppt>
<event></event>
</View>
<View id="71" did="62" type="RecyclerView">
<ppt>width=-1
height=-1</ppt>
<event></event>
</View>
<View id="63" did="58" type="LinearLayout">
<ppt>width=-1
height=40dp
orientation=horizontal
ut_alignParentBottom=true
paddingRight=10dp
paddingLeft=10dp
paddingBottom=10dp
</ppt>
<event></event>
</View>
<View id="73" did="63" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">sss lj="/storage/emulated/0"
us(72,"text",sss.lj)
fn fun.file_glq(sss.lj)
</eventItme></event>
</View>
<View id="74" did="73" type="TextView">
<ppt>width=-2
height=-2
text=根目录
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="65" did="63" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event></event>
</View>
<View id="67" did="63" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">fn fun.yc(51)</eventItme></event>
</View>
<View id="68" did="67" type="TextView">
<ppt>width=-2
height=-2
text=取消
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="69" did="63" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">fn fun.file_glq_syj()
</eventItme></event>
</View>
<View id="70" did="69" type="TextView">
<ppt>width=-2
height=-2
text=上一级
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="221" did="1" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
background=#3b000000
visibility=gone
</ppt>
<event><eventItme type="clicki">syso("")</eventItme></event>
</View>
<View id="222" did="221" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginBottom=20dp
layout_marginRight=20dp
app_CardcornerRadius=10dp
app_CardElevation=0
layout_marginTop=50dp
layout_marginBottom=50dp</ppt>
<event></event>
</View>
<View id="223" did="222" type="RelativeLayout">
<ppt>width=-1
height=-1</ppt>
<event></event>
</View>
<View id="224" did="223" type="LinearLayout">
<ppt>width=-1
height=-2
orientation=vertical
gravity=center_vertical</ppt>
<event></event>
</View>
<View id="225" did="224" type="TextView">
<ppt>width=-2
height=-2
text=上传记录
textStyle=bold
textSize=17sp
textColor=#ff1f999b
layout_marginTop=20dp
layout_marginLeft=20dp</ppt>
<event></event>
</View>
<View id="226" did="224" type="TextView">
<ppt>width=-2
height=-2
text=
textColor=#ff1f999b
layout_marginLeft=20dp
layout_marginBottom=5dp</ppt>
<event></event>
</View>
<View id="227" did="223" type="ScrollView">
<ppt>width=-1
height=-1
ut_below=224
ut_above=230
layout_marginBottom=20dp
scrollbars=none
overScrollMode=never</ppt>
<event></event>
</View>
<View id="228" did="227" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical</ppt>
<event></event>
</View>
<View id="229" did="228" type="RecyclerView">
<ppt>width=-1
height=-1</ppt>
<event></event>
</View>
<View id="230" did="223" type="LinearLayout">
<ppt>width=-1
height=40dp
orientation=horizontal
ut_alignParentBottom=true
paddingRight=10dp
paddingLeft=10dp
paddingBottom=10dp
</ppt>
<event></event>
</View>
<View id="231" did="230" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event></event>
</View>
<View id="232" did="231" type="TextView">
<ppt>width=-2
height=-2
text=
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="233" did="230" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event></event>
</View>
<View id="234" did="230" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">fn fun.yc(221)</eventItme></event>
</View>
<View id="235" did="234" type="TextView">
<ppt>width=-2
height=-2
text=取消
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="236" did="230" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">fn fun.deldir("$jl")
fn fun.file_jl("$jl")</eventItme></event>
</View>
<View id="237" did="236" type="TextView">
<ppt>width=-2
height=-2
text=清空
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="122" did="1" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
background=#3b000000
visibility=gone</ppt>
<event><eventItme type="clicki">syso("")</eventItme></event>
</View>
<View id="108" did="122" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginBottom=20dp
layout_marginRight=20dp
app_CardElevation=0
app_CardcornerRadius=10dp</ppt>
<event></event>
</View>
<View id="109" did="108" type="RelativeLayout">
<ppt>width=-1
height=-1</ppt>
<event></event>
</View>
<View id="110" did="109" type="LinearLayout">
<ppt>width=-1
height=-2
orientation=vertical</ppt>
<event></event>
</View>
<View id="111" did="110" type="TextView">
<ppt>width=-2
height=-2
text=文件名
textColor=#ff1f999b
textStyle=bold
textSize=17sp
layout_marginTop=20dp
layout_marginLeft=20dp
layout_marginRight=20dp</ppt>
<event></event>
</View>
<View id="112" did="110" type="TextView">
<ppt>width=-2
height=-2
text=是否上传该文件
textColor=#ff1f999b
layout_marginLeft=20dp
layout_marginTop=10dp
layout_marginBottom=10dp</ppt>
<event></event>
</View>
<View id="113" did="109" type="LinearLayout">
<ppt>width=-1
height=40dp
orientation=horizontal
ut_below=110
layout_marginBottom=10dp
</ppt>
<event></event>
</View>
<View id="114" did="113" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event></event>
</View>
<View id="116" did="113" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event></event>
</View>
<View id="117" did="113" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">fn fun.yc(122)</eventItme></event>
</View>
<View id="118" did="117" type="TextView">
<ppt>width=-2
height=-2
text=取消
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="119" did="113" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">t()
{
  ufnsui()
  {
    fn fun.yc(122)
    fn fun.xs(170)
    us(177,"text","上传中")
  }
  stop(1000)
  ss(sss.lj+"/"+sss.wjm,sc)
  syso(sc)
  huf("https://file.8845.top/api/upfile.php",null,sc,"utf-8",re)
  syso(re)
  f(re==)
  {
    ufnsui()
    {
      fn fun.yc(170)
      tw("连接超时")
    }
  }
  else
  {
    ufnsui()
    {
      json(re,rs)
      syso(rs)
      json(rs,"get","status",status)
      json(rs,"get","msg",msg)
      f(status==1)
      {
        ufnsui()
        {
          json(rs,"get","get_code",gcode)
          us(162,"text",gcode)
          us(153,"text",msg)
          sl(sss.wjm,".",szs)
          sgszl(szs,gh)
          s(gh-1,sls)
          sgsz(szs,sls,sss.hz)
          
          call(jgs, "mjava", "md5.md5st",sss.wjm)
          ss(jgs+"."+sss.hz,sss.dz)
          ss("https://file.8845.top/file/"+gcode+"/"+sss.dz,sss.uqr)
          us(154,"text",sss.uqr)
          uqr(sss.uqr,1200,ss.tp)
          us(252,"src",ss.tp)
          fn fun.yc(170)
          fn fun.xs(149)
          
          //上传记录
          time(4,sjj)
          fw("$jl/"+sjj+"/filename",sss.wjm)
          fw("$jl/"+sjj+"/gcode",gcode)
          
        }
      }
      else
      {
        ufnsui()
        {
          fn fun.yc(170)
          tw(msg)
        }
      }
    }
  }
}</eventItme></event>
</View>
<View id="120" did="119" type="TextView">
<ppt>width=-2
height=-2
text=确定
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="149" did="1" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
background=#3b000000
visibility=gone</ppt>
<event><eventItme type="clicki">syso("")</eventItme></event>
</View>
<View id="150" did="149" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginBottom=20dp
layout_marginRight=20dp
app_CardElevation=0
app_CardcornerRadius=10dp</ppt>
<event></event>
</View>
<View id="151" did="150" type="RelativeLayout">
<ppt>width=-1
height=-1</ppt>
<event></event>
</View>
<View id="152" did="151" type="LinearLayout">
<ppt>width=-1
height=-2
orientation=vertical</ppt>
<event></event>
</View>
<View id="153" did="152" type="TextView">
<ppt>width=-2
height=-2
text=msg
textColor=#ff1f999b
textStyle=bold
textSize=17sp
layout_marginTop=20dp
layout_marginLeft=20dp
layout_marginRight=20dp</ppt>
<event></event>
</View>
<View id="169" did="152" type="TextView">
<ppt>width=-2
height=-2
text=文件地址：
textColor=#ff1f999b
layout_marginLeft=20dp
layout_marginTop=10dp
textIsSelectable=true
textStyle=bold

</ppt>
<event></event>
</View>
<View id="154" did="152" type="TextView">
<ppt>width=-2
height=-2
text=website
textColor=#ff1f999b
layout_marginLeft=20dp
layout_marginTop=10dp
textIsSelectable=true
textStyle=bold
textColorLink=#fff98b8b
autoLink=web

</ppt>
<event></event>
</View>
<View id="165" did="152" type="TextView">
<ppt>width=-2
height=-2
text=取件码：
textColor=#ff1f999b
layout_marginLeft=20dp
layout_marginTop=10dp
textIsSelectable=true
textStyle=bold</ppt>
<event></event>
</View>
<View id="162" did="152" type="TextView">
<ppt>width=-2
height=-2
text=00000000
textColor=#fff98b8b
layout_marginLeft=20dp
layout_marginTop=10dp
textIsSelectable=true
textStyle=bold
</ppt>
<event></event>
</View>
<View id="253" did="152" type="TextView">
<ppt>width=-2
height=-2
text=文件二维码：
textColor=#ff1f999b
layout_marginLeft=20dp
layout_marginTop=10dp
textIsSelectable=true
textStyle=bold</ppt>
<event></event>
</View>
<View id="251" did="152" type="LinearLayout">
<ppt>width=-1
height=-2
orientation=vertical
gravity=center_vertical</ppt>
<event></event>
</View>
<View id="252" did="251" type="ImageView">
<ppt>width=120dp
height=120dp
src=17301524
layout_marginBottom=10dp
layout_marginLeft=10dp</ppt>
<event></event>
</View>
<View id="155" did="151" type="LinearLayout">
<ppt>width=-1
height=40dp
orientation=horizontal
ut_below=152
layout_marginBottom=10dp
</ppt>
<event></event>
</View>
<View id="157" did="155" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1
</ppt>
<event><eventItme type="clicki">fn fun.yc(149)
time(4,sti)
bfs(ss.tp,"/storage/emulated/0/Download/文件快递/二维码/"+sti+".png")
tw("已保存至 根目录/下载/文件快递/二维码 ")
</eventItme></event>
</View>
<View id="254" did="157" type="TextView">
<ppt>width=-2
height=-2
text=保存二维码
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="163" did="155" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1
visibility=gone</ppt>
<event></event>
</View>
<View id="164" did="163" type="TextView">
<ppt>width=-2
height=-2
text=确定
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="158" did="155" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">ug(162,"text",qjm)
sxb(qjm)
fn fun.yc(149)
tw("已复制")</eventItme></event>
</View>
<View id="159" did="158" type="TextView">
<ppt>width=-2
height=-2
text=复制取件码
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="160" did="155" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">fn fun.yc(149)</eventItme></event>
</View>
<View id="161" did="160" type="TextView">
<ppt>width=-2
height=-2
text=确定
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="170" did="1" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
background=#3b000000
visibility=gone</ppt>
<event><eventItme type="clicki">syso("")</eventItme></event>
</View>
<View id="171" did="170" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginBottom=20dp
layout_marginRight=20dp
app_CardElevation=0
app_CardcornerRadius=10dp</ppt>
<event></event>
</View>
<View id="172" did="171" type="RelativeLayout">
<ppt>width=-1
height=-1</ppt>
<event></event>
</View>
<View id="173" did="172" type="LinearLayout">
<ppt>width=-1
height=-2
orientation=horizontal
gravity=center_vertical</ppt>
<event></event>
</View>
<View id="176" did="173" type="ProgressBar">
<ppt>width=25dp
height=25dp
style=16842873
layout_marginLeft=20dp
layout_marginTop=20dp
layout_marginBottom=20dp</ppt>
<event></event>
</View>
<View id="177" did="173" type="TextView">
<ppt>width=-2
height=-2
text=
layout_marginLeft=20dp
textColor=#ff1f999b
textStyle=bold
textSize=15sp</ppt>
<event></event>
</View>
<View id="255" did="1" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
background=#3b000000
visibility=gone</ppt>
<event><eventItme type="clicki">fn fun.yc(255)</eventItme></event>
</View>
<View id="256" did="255" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginBottom=20dp
layout_marginRight=20dp
app_CardcornerRadius=10dp
app_CardElevation=0</ppt>
<event></event>
</View>
<View id="257" did="256" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
</ppt>
<event></event>
</View>
<View id="258" did="257" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center</ppt>
<event></event>
</View>
<View id="268" did="258" type="ImageView">
<ppt>width=-1
height=-2
src=@bt.jpg</ppt>
<event></event>
</View>
<View id="260" did="257" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center</ppt>
<event></event>
</View>
<View id="261" did="257" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center</ppt>
<event></event>
</View>
<View id="262" did="261" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginRight=20dp
app_CardcornerRadius=10dp
app_CardElevation=0
layout_marginTop=10dp
layout_marginBottom=10dp
app_CardBackgroundColor=#ff1f999b
</ppt>
<event></event>
</View>
<View id="263" did="262" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
clickable=true
BackgroundRipple=#3b000000</ppt>
<event><eventItme type="clicki">t()
{
  ufnsui()
  {
    //fn fun.yc(255)
    javanew(ss.弹窗,"com.google.android.material.bottomsheet.BottomSheetDialog","Context",activity)
    nvw(-1,null,"线性布局","width=-1\nheight=-1\norientation=vertical\nbackgroundcolor=0",ss.载体)
    addv(ss.载体,"捐赠.iyu")
    java(null,ss.弹窗,"android.app.Dialog.setContentView","android.view.View",ss.载体)
    java(null,ss.弹窗,"android.app.Dialog.setCancelable","boolean",true)
    java(null,ss.弹窗,"android.app.Dialog.setCanceledOnTouchOutside","boolean",true)
    java(delegate,ss.弹窗,"androidx.appcompat.app.AppCompatDialog.getDelegate")
    javags(id,null,"com.google.android.material.R$id","design_bottom_sheet")
    java(弹窗根控件,delegate,"androidx.appcompat.app.AppCompatDelegate.findViewById","int",id)
    us(弹窗根控件,"backgroundcolor",0)
    java(null,ss.弹窗,"android.app.Dialog.show")
    javags(behavior,ss.弹窗,"com.google.android.material.bottomsheet.BottomSheetDialog","mBehavior")
    gvs(ss.载体,捐赠.8,图片)
    us(图片,"src","@juan/wx.png")
  }
}</eventItme></event>
</View>
<View id="264" did="263" type="TextView">
<ppt>width=-2
height=-2
text=微信捐赠
textColor=#ffffff
textStyle=bold
textSize=15sp
layout_marginTop=10dp
layout_marginBottom=10dp</ppt>
<event></event>
</View>
<View id="265" did="261" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginRight=20dp
app_CardcornerRadius=10dp
app_CardElevation=0
layout_marginTop=10dp
layout_marginBottom=20dp
app_CardBackgroundColor=#ff099dff</ppt>
<event></event>
</View>
<View id="266" did="265" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
clickable=true
BackgroundRipple=#3b000000</ppt>
<event><eventItme type="clicki">t()
{
  ufnsui()
  {
    //fn fun.yc(255)
    javanew(ss.弹窗,"com.google.android.material.bottomsheet.BottomSheetDialog","Context",activity)
    nvw(-1,null,"线性布局","width=-1\nheight=-1\norientation=vertical\nbackgroundcolor=0",ss.载体)
    addv(ss.载体,"捐赠.iyu")
    java(null,ss.弹窗,"android.app.Dialog.setContentView","android.view.View",ss.载体)
    java(null,ss.弹窗,"android.app.Dialog.setCancelable","boolean",true)
    java(null,ss.弹窗,"android.app.Dialog.setCanceledOnTouchOutside","boolean",true)
    java(delegate,ss.弹窗,"androidx.appcompat.app.AppCompatDialog.getDelegate")
    javags(id,null,"com.google.android.material.R$id","design_bottom_sheet")
    java(弹窗根控件,delegate,"androidx.appcompat.app.AppCompatDelegate.findViewById","int",id)
    us(弹窗根控件,"backgroundcolor",0)
    java(null,ss.弹窗,"android.app.Dialog.show")
    javags(behavior,ss.弹窗,"com.google.android.material.bottomsheet.BottomSheetDialog","mBehavior")
    gvs(ss.载体,捐赠.8,图片)
    us(图片,"src","@juan/zfb.png")
  }
}</eventItme></event>
</View>
<View id="267" did="266" type="TextView">
<ppt>width=-2
height=-2
text=支付宝捐赠
textColor=#ffffff
textStyle=bold
textSize=15sp
layout_marginTop=10dp
layout_marginBottom=10dp</ppt>
<event></event>
</View>
<View id="178" did="1" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
background=#3b000000
visibility=gone</ppt>
<event><eventItme type="clicki">syso("")</eventItme></event>
</View>
<View id="179" did="178" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginBottom=20dp
layout_marginRight=20dp
app_CardElevation=0
app_CardcornerRadius=10dp</ppt>
<event></event>
</View>
<View id="180" did="179" type="RelativeLayout">
<ppt>width=-1
height=-1</ppt>
<event></event>
</View>
<View id="181" did="180" type="LinearLayout">
<ppt>width=-1
height=-2
orientation=vertical</ppt>
<event></event>
</View>
<View id="182" did="181" type="TextView">
<ppt>width=-2
height=-2
text=正在下载
textColor=#ff1f999b
textStyle=bold
textSize=17sp
layout_marginTop=20dp
layout_marginLeft=20dp
layout_marginRight=20dp</ppt>
<event></event>
</View>
<View id="185" did="181" type="ProgressBar">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginRight=20dp
layout_marginTop=10dp
max=100
</ppt>
<event></event>
</View>
<View id="183" did="181" type="TextView">
<ppt>width=-2
height=-2
text=0%
textColor=#ff1f999b
layout_marginLeft=20dp
layout_marginTop=10dp
layout_marginBottom=20dp</ppt>
<event></event>
</View>
<View id="238" did="1" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
background=#3b000000
visibility=gone</ppt>
<event><eventItme type="clicki">syso("")</eventItme></event>
</View>
<View id="239" did="238" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginBottom=20dp
layout_marginRight=20dp
app_CardElevation=0
app_CardcornerRadius=10dp</ppt>
<event></event>
</View>
<View id="240" did="239" type="RelativeLayout">
<ppt>width=-1
height=-1</ppt>
<event></event>
</View>
<View id="241" did="240" type="LinearLayout">
<ppt>width=-1
height=-2
orientation=vertical</ppt>
<event></event>
</View>
<View id="242" did="241" type="TextView">
<ppt>width=-2
height=-2
text=公告
textColor=#ff1f999b
textStyle=bold
textSize=17sp
layout_marginTop=20dp
layout_marginLeft=20dp
layout_marginRight=20dp</ppt>
<event></event>
</View>
<View id="243" did="241" type="TextView">
<ppt>width=-2
height=-2
text=
textColor=#ff1f999b
layout_marginLeft=20dp
layout_marginTop=10dp
layout_marginBottom=10dp
layout_marginRight=20dp
textColorLink=#fff98b8b
autoLink=web
textIsSelectable=true
textStyle=bold</ppt>
<event></event>
</View>
<View id="244" did="240" type="LinearLayout">
<ppt>width=-1
height=40dp
orientation=horizontal
ut_below=241
layout_marginBottom=10dp
</ppt>
<event></event>
</View>
<View id="245" did="244" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event></event>
</View>
<View id="246" did="244" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event></event>
</View>
<View id="249" did="244" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1
visibility=invisible</ppt>
<event><eventItme type="clicki">t()
{
  ufnsui()
  {
    fn fun.yc(186)
    fn fun.xs(178)
  }
  stop(500)
  hdda(ss.gxlj,"/storage/emulated/0/Download/文件快递/",wjm,"","","","",v)
  w(true)
  {
    hddg(v, "downloadpercentage", bfb)
    hddg(v,"status",st)
    f(st=="2")
    {
      ufnsui()
      {
        fn fun.yc(178)
        tw("下载成功 已保存至 根目录/下载/文件快递/")
      }
      break
    }
    ufnsui()
    {
      us(183,"text",bfb+"%")
      us(185,"progress",bfb)
    }
  }
}</eventItme></event>
</View>
<View id="250" did="249" type="TextView">
<ppt>width=-2
height=-2
text=下载
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="247" did="244" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">fn fun.yc(238)</eventItme></event>
</View>
<View id="248" did="247" type="TextView">
<ppt>width=-2
height=-2
text=确定
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="186" did="1" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
background=#3b000000
visibility=gone</ppt>
<event><eventItme type="clicki">syso("")</eventItme></event>
</View>
<View id="187" did="186" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginBottom=20dp
layout_marginRight=20dp
app_CardElevation=0
app_CardcornerRadius=10dp</ppt>
<event></event>
</View>
<View id="188" did="187" type="RelativeLayout">
<ppt>width=-1
height=-1</ppt>
<event></event>
</View>
<View id="189" did="188" type="LinearLayout">
<ppt>width=-1
height=-2
orientation=vertical</ppt>
<event></event>
</View>
<View id="190" did="189" type="TextView">
<ppt>width=-2
height=-2
text=检测到新版本
textColor=#ff1f999b
textStyle=bold
textSize=17sp
layout_marginTop=20dp
layout_marginLeft=20dp
layout_marginRight=20dp</ppt>
<event></event>
</View>
<View id="191" did="189" type="TextView">
<ppt>width=-2
height=-2
text=*
textColor=#ff1f999b
layout_marginLeft=20dp
layout_marginTop=10dp
layout_marginBottom=10dp
layout_marginRight=20dp
textColorLink=#fff98b8b
autoLink=web
textIsSelectable=true
textStyle=bold
</ppt>
<event></event>
</View>
<View id="192" did="188" type="LinearLayout">
<ppt>width=-1
height=40dp
orientation=horizontal
ut_below=189
layout_marginBottom=10dp
</ppt>
<event></event>
</View>
<View id="193" did="192" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event></event>
</View>
<View id="194" did="192" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event></event>
</View>
<View id="195" did="192" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">fn fun.yc(186)</eventItme></event>
</View>
<View id="196" did="195" type="TextView">
<ppt>width=-2
height=-2
text=取消
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="197" did="192" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">t()
{
  ufnsui()
  {
    fn fun.yc(186)
    fn fun.xs(178)
  }
  stop(500)
  sran(100000,999999,sji)
  hdda(ss.gxlj,"/storage/emulated/0/Download/文件快递/",sji+".apk","","","","",v)
  w(true)
  {
    hddg(v, "downloadpercentage", bfb)
    hddg(v,"status",st)
    f(st=="2")
    {
      ufnsui()
      {
        fn fun.yc(178)
        tw("下载成功 已保存至 根目录/下载/文件快递/")
      }
      break
    }
    ufnsui()
    {
      us(183,"text",bfb+"%")
      us(185,"progress",bfb)
    }
  }
}</eventItme></event>
</View>
<View id="198" did="197" type="TextView">
<ppt>width=-2
height=-2
text=下载
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="123" did="1" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
background=#3b000000
visibility=gone</ppt>
<event><eventItme type="clicki">syso("")</eventItme></event>
</View>
<View id="124" did="123" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginBottom=20dp
layout_marginRight=20dp
app_CardElevation=0
app_CardcornerRadius=10dp</ppt>
<event></event>
</View>
<View id="125" did="124" type="RelativeLayout">
<ppt>width=-1
height=-1</ppt>
<event></event>
</View>
<View id="126" did="125" type="LinearLayout">
<ppt>width=-1
height=-2
orientation=vertical</ppt>
<event></event>
</View>
<View id="127" did="126" type="TextView">
<ppt>width=-2
height=-2
text=退出软件
textColor=#ff1f999b
textStyle=bold
textSize=17sp
layout_marginTop=20dp
layout_marginLeft=20dp
layout_marginRight=20dp</ppt>
<event></event>
</View>
<View id="128" did="126" type="TextView">
<ppt>width=-2
height=-2
text=确定要退出吗
textColor=#ff1f999b
layout_marginLeft=20dp
layout_marginTop=10dp
layout_marginBottom=10dp</ppt>
<event></event>
</View>
<View id="129" did="125" type="LinearLayout">
<ppt>width=-1
height=40dp
orientation=horizontal
ut_below=126
layout_marginBottom=10dp
</ppt>
<event></event>
</View>
<View id="130" did="129" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event></event>
</View>
<View id="131" did="129" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event></event>
</View>
<View id="132" did="129" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">fn fun.yc(123)
ss tczt=1</eventItme></event>
</View>
<View id="133" did="132" type="TextView">
<ppt>width=-2
height=-2
text=再看看
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="134" did="129" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center
layout_weight=1</ppt>
<event><eventItme type="clicki">end()</eventItme></event>
</View>
<View id="135" did="134" type="TextView">
<ppt>width=-2
height=-2
text=确定
textColor=#ff1f999b
textStyle=bold</ppt>
<event></event>
</View>
<View id="29" did="0" type="RadioButton">
<ppt>width=-2
height=-2
text=单选项29</ppt>
<event></event>
</View>
<UIEventset><eventItme type="loading">uycl("#3b000000",true)
ss tcxs=0
ss tczt=1

ngde(3,20,0,"#ff1f999b",bl)
us(14,"background",bl)
ngde(3,20,"#ff1f999b",0,bls)
us(19,"background",bls)

ngde(3,20,0,"#ff1f999b",bl)
us(30,"background",bl)
ngde(3,20,"#ff1f999b",0,bls)
us(33,"background",bls)

ngde(3,20,0,"#ff1f999b",bl)
us(45,"background",bl)
ngde(3,20,"#ff1f999b",0,bls)
us(48,"background",bls)

//us(154,"text",https://file.8845.top)

fn 着色.控件着色(176,"#ff1f999b")
fn 着色.控件着色(185,"#ff1f999b")

t()
{
  hs("https://file.8845.top/api/get_juan.php",re)
  syso(re)
  f(re==)
  {
  }
  else
  {
    json(re,js)
    json(js,"get","vis",vis)
    f(vis=="true")
    {
      ufnsui()
      {
        fn fun.xs(255)
      }
    }
  }
}

addv(1,"")
us(hengfu_2.16,"visibility","gone")

</eventItme><eventItme type="onactivityresult">javags(er,activity,"android.app.Activity","RESULT_OK")
f(st_lC==er)
{
  f(st_sC==2)
  {
    java(uri,st_iT,"android.content.Intent.getData")
    call(ss.path, "mjava", "uri.getRealPath", activity, uri)
    syso(ss.path)
    s lj=ss.path
    f(ss.path==)
    {
    }
    else
    {
      f(lj=="null"||lj=="false")
      {
      }
      else
      {
        sl(ss.path,"/",sz)
        sgszl(sz,gs)
        s(gs-1,wjxh)
        sgsz(sz,wjxh,sss.wjm)
        syso(sss.wjm)
        sl(sss.wjm,".",szs)
        sgszl(szs,gh)
        s(gh-1,sls)
        sgsz(szs,sls,sss.hz)
        call(jgs, "mjava", "md5.md5st",sss.wjm)
        ss(jgs+"."+sss.hz,sss.dz)
        t()
        {
          ufnsui()
          {
            fn fun.xs(170)
            us(177,"text","上传中")
          }
          stop(1000)
          //ss("https://file.8845.top/"+sss.wjm,sc)
          syso(sc)
          huf("https://file.8845.top/api/upfile.php",null,lj,"utf-8",re)
          syso(re)
          f(re==)
          {
            ufnsui()
            {
              fn fun.yc(170)
              tw("连接超时")
            }
          }
          else
          {
            ufnsui()
            {
              
              json(re,rs)
              syso(rs)
              json(rs,"get","status",status)
              json(rs,"get","msg",msg)
              f(status==1)
              {
                ufnsui()
                {
                  json(rs,"get","get_code",gcode)
                  us(162,"text",gcode)
                  us(153,"text",msg)
                  ss("https://file.8845.top/file/"+gcode+"/"+sss.dz,sss.uqr)
                  us(154,"text",sss.uqr)
                  uqr(sss.uqr,1200,ss.tp)
                  us(252,"src",ss.tp)
                  fn fun.yc(170)
                  fn fun.xs(149)
                  
                  //上传记录
                  time(4,sjj)
                  fw("$jl/"+sjj+"/filename",sss.wjm)
                  fw("$jl/"+sjj+"/gcode",gcode)
                }
              }
              else
              {
                ufnsui()
                {
                  fn fun.yc(170)
                  tw(msg)
                }
              }
            }
          }
        }
      }
    }
  }
}</eventItme><eventItme type="downkey">syso(st_kC)
f(st_kC==4)
{
  f(ss.tczt==1)
  {
    fn fun.xs(123)
    ss tczt=0
  }
  else f(ss.tczt==0)
  {
    fn fun.yc(123)
    ss tczt=1
  }
  /.
  f(sss.exit==null)
  {
  sss exit=0
  
  t()
  {
  stop(1200)
  sss exit=null
  }
  }
  else
  {
  end()
  }
  ./
}
<eventItme type="loadingComplete">t()
{
  hs("https://file.8845.top/api/get_up.php",re)
  syso(re)
  f(re==)
  {
  }
  else
  {
    json(re,js)
    json(js,"get","bbh",bbh)
    json(js,"get","gxnr",ss.gxnr)
    json(js,"get","gxlj",ss.gxlj)
    json(js,"get","is_force_update",is_force_update)
    
    f(bbh&gt;sss.bbh)
    {
      ufnsui()
      {
        fn fun.xs(186)
        us(191,"text",ss.gxnr)
        
        // 检查是否强制更新
        f(is_force_update == "true")
        {
          // 隐藏并禁用“暂不更新”按钮
          us(195, "visibility", "gone")
        }
        else
        {
          // 显示“暂不更新”按钮
          us(195, "visibility", "visible")
        }
      }
    }
  }
}



t()
{
  hs("https://file.8845.top/api/get_gg.php",re)
  syso(re)
  f(re==)
  {
  }
  else
  {
    json(re,js)
    json(js,"get","vis",vis)
    json(js,"get","ggnr",ss.ggnr)
    f(vis=="true")
    {
      ufnsui()
      {
        fn fun.xs(238)
        us(243,"text",ss.ggnr)
      }
    }
  }
}
<eventItme>
//fn ad.insert_2()
fn noad.Is_noAd()
f(sss.noad==false)
{
  
  fn ad.config()
  us(hengfu_2.16,"visibility","gone")
  t()
  {
    s iconid="hengfu_2.15"
    //图片id(广告图标)
    s titleid="hengfu_2.10"
    //广告名称(标题)id
    s contentid="hengfu_2.11"
    //广告内容控件id
    s gbid="hengfu_2.13"
    //关闭控件id
    s bjid="hengfu_2.16"
    //被载入页面父控件id
    stop(1000)
    time(4,t)
    //计算token
    ss(sss.appid+sss.appkey+sss.ukey+t,d)
    call(token, "mjava", "md5.md5st",d)
    //提交参数
    sss type=2
    s sk=3
    ss("appid="+sss.appid+"&appkey="+sss.appkey+"&token="+token+"&type="+sss.type+"&sk="+sk+"&t="+t+"&posid="+sss.posid,get)
    
    hs(sss.api+"/ad/req?"+get,j)
    f(j==)
    {
      ufnsui()
      {
        
        tw("网络错误")
      }
    }
    else
    {
      json(j,js)
      json(js,"get","error",error)
      json(js,"get","m",m)
      f(error==1000)
      {
        json(js,"get","ad",ad)
        json(ad,"get","adid",ss.adid)
        json(ad,"get","eid",ss.eid)
        json(ad,"get","aurl",ss.aurl)
        json(ad,"get","icon",icon)
        json(ad,"get","title",title)
        json(ad,"get","content",content)
        ufnsui()
        {
          us(hengfu_2.16,"visibility",true)
          us(iconid,"src",icon)
          us(titleid,"text",title)
          us(contentid,"text",content)
          ssj(gbid,"clicki")
          {
            ufnsui()
            {
              us(hengfu_2.16,"visibility","gone")
              fn ad.es(ss.adid,ss.eid)
            }
          }
          ssj(bjid,"clicki")
          {
            ufnsui()
            {
              sss website=ss.aurl
              uigo("浏览器.iyu")
              fn ad.es(ss.adid,ss.eid)
            }
          }
          
        }
      }
      else
      {
        /.
        ufnsui()
        {
        
        tw(m)
        }
        ./
        
      }
    }
  }
}</eventItme><eventItme type="resume">
f(sss.XsupAn==true)
{
  fn fun.xs(36)
  sss XsupAn=false
}</eventItme><eventItme type="start">//免广告权益实现(获取)
fn noad.Is_noAd()
f(sss.noad==true)
{
  us(hengfu_2.16,"visibility","gone")
  
  
}</eventItme></UIEventset>