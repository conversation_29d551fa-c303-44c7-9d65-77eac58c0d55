<template>
  <view class="container">
    <!-- 头部 -->
    <view class="header">
      <text class="header-title">捐赠支持</text>
      <text class="header-subtitle">您的支持是我们前进的动力</text>
    </view>
    
    <!-- 捐赠说明 -->
    <view class="donate-info">
      <view class="info-card">
        <text class="info-title">为什么需要捐赠？</text>
        <text class="info-content">
          文件快递服务需要服务器维护费用、域名费用等成本支出。您的捐赠将帮助我们：
          • 维持服务器稳定运行
          • 提升文件传输速度
          • 开发更多实用功能
          • 提供更好的用户体验
        </text>
      </view>
    </view>
    
    <!-- 捐赠方式 -->
    <view class="donate-methods">
      <text class="section-title">选择捐赠方式</text>
      
      <view class="method-card" @click="showQRCode('alipay')">
        <image class="method-icon" src="/static/images/alipay-icon.png" mode="aspectFit"></image>
        <view class="method-info">
          <text class="method-name">支付宝</text>
          <text class="method-desc">扫码支付，安全便捷</text>
        </view>
        <text class="method-arrow">></text>
      </view>
      
      <view class="method-card" @click="showQRCode('wechat')">
        <image class="method-icon" src="/static/images/wechat-icon.png" mode="aspectFit"></image>
        <view class="method-info">
          <text class="method-name">微信支付</text>
          <text class="method-desc">微信扫码，快速支付</text>
        </view>
        <text class="method-arrow">></text>
      </view>
    </view>
    
    <!-- 捐赠记录 -->
    <view class="donate-records" v-if="donateRecords.length > 0">
      <text class="section-title">感谢以下用户的支持</text>
      <scroll-view class="records-list" scroll-y="true">
        <view class="record-item" v-for="(record, index) in donateRecords" :key="index">
          <text class="record-name">{{ record.name }}</text>
          <text class="record-amount">¥{{ record.amount }}</text>
          <text class="record-time">{{ record.time }}</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 二维码对话框 -->
    <view class="modal-overlay" v-if="showQRDialog" @click="hideQRDialog">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{ currentPayMethod === 'alipay' ? '支付宝' : '微信' }}捐赠</text>
        </view>
        <view class="modal-body">
          <image class="qr-code" :src="currentQRCode" mode="aspectFit"></image>
          <text class="qr-desc">请使用{{ currentPayMethod === 'alipay' ? '支付宝' : '微信' }}扫描二维码</text>
        </view>
        <view class="modal-actions">
          <button class="modal-btn" @click="saveQRCode">保存二维码</button>
          <button class="modal-btn primary" @click="hideQRDialog">关闭</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getDonateInfo } from '@/utils/api.js'
import { showToast } from '@/utils/common.js'

export default {
  data() {
    return {
      showQRDialog: false,
      currentPayMethod: '',
      currentQRCode: '',
      donateRecords: [],
      qrCodes: {
        alipay: '/static/images/alipay-qr.png',
        wechat: '/static/images/wechat-qr.png'
      }
    }
  },
  
  onLoad() {
    this.loadDonateInfo()
  },
  
  methods: {
    // 加载捐赠信息
    async loadDonateInfo() {
      try {
        const result = await getDonateInfo()
        if (result.status === 1) {
          this.donateRecords = result.records || []
          // 更新二维码地址
          if (result.qrCodes) {
            this.qrCodes = { ...this.qrCodes, ...result.qrCodes }
          }
        }
      } catch (error) {
        console.error('加载捐赠信息失败:', error)
      }
    },
    
    // 显示二维码
    showQRCode(method) {
      this.currentPayMethod = method
      this.currentQRCode = this.qrCodes[method]
      this.showQRDialog = true
    },
    
    // 隐藏二维码对话框
    hideQRDialog() {
      this.showQRDialog = false
      this.currentPayMethod = ''
      this.currentQRCode = ''
    },
    
    // 保存二维码
    saveQRCode() {
      // 这里实现保存二维码到相册的逻辑
      uni.saveImageToPhotosAlbum({
        filePath: this.currentQRCode,
        success: () => {
          showToast('二维码已保存到相册')
        },
        fail: () => {
          showToast('保存失败，请手动截图保存')
        }
      })
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 头部 */
.header {
  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);
  padding: 40px 20px 30px;
  color: #ffffff;
  text-align: center;
}

.header-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.header-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

/* 捐赠说明 */
.donate-info {
  padding: 20px;
}

.info-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12px;
}

.info-content {
  font-size: 14px;
  color: #666666;
  line-height: 1.6;
}

/* 捐赠方式 */
.donate-methods {
  padding: 0 20px 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15px;
}

.method-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.method-card:active {
  transform: scale(0.98);
}

.method-icon {
  width: 40px;
  height: 40px;
  margin-right: 15px;
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 4px;
}

.method-desc {
  font-size: 12px;
  color: #999999;
}

.method-arrow {
  font-size: 18px;
  color: #cccccc;
  font-weight: bold;
}

/* 捐赠记录 */
.donate-records {
  padding: 0 20px 20px;
}

.records-list {
  background-color: #ffffff;
  border-radius: 12px;
  max-height: 200px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.record-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-name {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  flex: 1;
}

.record-amount {
  font-size: 14px;
  color: #1f999b;
  font-weight: bold;
  margin-right: 15px;
}

.record-time {
  font-size: 12px;
  color: #999999;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #ffffff;
  border-radius: 15px;
  margin: 20px;
  max-width: 320px;
  width: 90%;
  overflow: hidden;
}

.modal-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.modal-body {
  padding: 30px 20px;
  text-align: center;
}

.qr-code {
  width: 200px;
  height: 200px;
  margin-bottom: 15px;
  border-radius: 8px;
}

.qr-desc {
  font-size: 14px;
  color: #666666;
}

.modal-actions {
  display: flex;
  border-top: 1px solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 15px;
  border: none;
  background-color: #ffffff;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  border-right: 1px solid #f0f0f0;
}

.modal-btn:last-child {
  border-right: none;
}

.modal-btn.primary {
  color: #1f999b;
}

.modal-btn:active {
  background-color: #f8f8f8;
}
</style>
