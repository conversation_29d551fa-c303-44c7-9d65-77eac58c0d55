<template>
  <view class="loading-container">
    <view class="loading-content">
      <view class="spinner-container">
        <view class="spinner"></view>
      </view>
      <text class="loading-text">{{ loadingText }}</text>
      <text class="loading-desc">{{ loadingDesc }}</text>
    </view>
    
    <!-- 进度条 -->
    <view class="progress-container" v-if="showProgress">
      <view class="progress-bar">
        <view class="progress-fill" :style="{ width: progress + '%' }"></view>
      </view>
      <text class="progress-text">{{ progress }}%</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loadingText: '加载中...',
      loadingDesc: '正在初始化应用',
      showProgress: false,
      progress: 0
    }
  },
  
  onLoad(options) {
    // 根据传入的参数设置加载文本
    if (options.type) {
      this.setLoadingType(options.type)
    }
    
    this.startLoading()
  },
  
  methods: {
    setLoadingType(type) {
      switch (type) {
        case 'upload':
          this.loadingText = '上传中...'
          this.loadingDesc = '正在上传文件，请稍候'
          this.showProgress = true
          break
        case 'download':
          this.loadingText = '下载中...'
          this.loadingDesc = '正在下载文件，请稍候'
          this.showProgress = true
          break
        case 'init':
          this.loadingText = '初始化中...'
          this.loadingDesc = '正在加载应用数据'
          break
        default:
          this.loadingText = '加载中...'
          this.loadingDesc = '请稍候...'
      }
    },
    
    startLoading() {
      // 模拟加载进度
      if (this.showProgress) {
        this.simulateProgress()
      } else {
        // 简单的加载延迟
        setTimeout(() => {
          this.completeLoading()
        }, 2000)
      }
    },
    
    simulateProgress() {
      const interval = setInterval(() => {
        if (this.progress < 90) {
          this.progress += Math.random() * 10
        } else if (this.progress < 100) {
          this.progress += 1
        } else {
          clearInterval(interval)
          this.completeLoading()
        }
      }, 200)
    },
    
    completeLoading() {
      // 加载完成，返回上一页或跳转到指定页面
      setTimeout(() => {
        uni.navigateBack({
          fail: () => {
            // 如果无法返回，则跳转到首页
            uni.reLaunch({
              url: '/pages/index/index'
            })
          }
        })
      }, 500)
    },
    
    // 外部调用更新进度
    updateProgress(progress, text) {
      this.progress = progress
      if (text) {
        this.loadingDesc = text
      }
    }
  }
}
</script>

<style scoped>
.loading-container {
  width: 100%;
  height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60px;
}

.spinner-container {
  margin-bottom: 30px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1f999b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 18px;
  font-weight: bold;
  color: #1f999b;
  margin-bottom: 10px;
}

.loading-desc {
  font-size: 14px;
  color: #666666;
  text-align: center;
  line-height: 1.5;
}

.progress-container {
  width: 80%;
  max-width: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1f999b 0%, #16a085 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #999999;
  font-weight: 500;
}
</style>
