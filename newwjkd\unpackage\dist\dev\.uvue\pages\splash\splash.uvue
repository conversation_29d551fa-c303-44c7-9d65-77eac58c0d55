
import { getNoAdStatus } from '@/utils/storage.js'

const __sfc__ = defineComponent({
  data() {
    return {
      version: '2.1'
    }
  },
  
  onLoad() {
    this.initApp()
  },
  
  methods: {
    async initApp() {
      try {
        // 检查免广告状态
        const noAd = getNoAdStatus()
        
        // 模拟启动延迟
        await this.delay(2000)
        
        // 根据广告状态决定跳转页面
        if (noAd) {
          // 直接跳转到主页
          uni.reLaunch({
            url: '/pages/index/index'
          })
        } else {
          // 跳转到加载页（可以在这里显示广告）
          uni.reLaunch({
            url: '/pages/index/index'
          })
        }
      } catch (error) {
        console.error('启动初始化失败:', error, " at pages/splash/splash.uvue:51")
        // 出错时直接跳转到主页
        uni.reLaunch({
          url: '/pages/index/index'
        })
      }
    },
    
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
})

export default __sfc__
function GenPagesSplashSplashRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
  return createElementVNode("view", utsMapOf({ class: "splash-container" }), [
    createElementVNode("view", utsMapOf({ class: "content" }), [
      createElementVNode("image", utsMapOf({
        class: "logo",
        src: "/static/images/logo.png",
        mode: "aspectFit"
      })),
      createElementVNode("text", utsMapOf({ class: "app-name" }), "文件快递服务"),
      createElementVNode("text", utsMapOf({ class: "version" }), "v" + toDisplayString(_ctx.version), 1 /* TEXT */)
    ]),
    createElementVNode("view", utsMapOf({ class: "loading" }), [
      createElementVNode("text", utsMapOf({ class: "loading-text" }), "正在启动...")
    ])
  ])
}
const GenPagesSplashSplashStyles = [utsMapOf([["splash-container", padStyleMapOf(utsMapOf([["width", "100%"], ["backgroundImage", "linear-gradient(135deg, #1f999b 0%, #16a085 100%)"], ["display", "flex"], ["flexDirection", "column"], ["justifyContent", "center"], ["alignItems", "center"], ["position", "relative"]]))], ["content", padStyleMapOf(utsMapOf([["display", "flex"], ["flexDirection", "column"], ["alignItems", "center"], ["marginBottom", 100]]))], ["logo", padStyleMapOf(utsMapOf([["width", 120], ["height", 120], ["marginBottom", 30], ["borderRadius", 20], ["boxShadow", "0 8px 20px rgba(0, 0, 0, 0.2)"], ["animation", "logoFloat 3s ease-in-out infinite"]]))], ["app-name", padStyleMapOf(utsMapOf([["fontSize", 28], ["fontWeight", "bold"], ["color", "#ffffff"], ["marginBottom", 10], ["textShadow", "0 2px 4px rgba(0, 0, 0, 0.3)"]]))], ["version", padStyleMapOf(utsMapOf([["fontSize", 16], ["color", "rgba(255,255,255,0.8)"]]))], ["loading", padStyleMapOf(utsMapOf([["position", "absolute"], ["bottom", 80], ["display", "flex"], ["alignItems", "center"]]))], ["loading-text", padStyleMapOf(utsMapOf([["fontSize", 16], ["color", "rgba(255,255,255,0.9)"], ["marginLeft", 10], ["animation", "textBlink 1.5s ease-in-out infinite"]]))], ["@FONT-FACE", utsMapOf([["0", utsMapOf([])], ["1", utsMapOf([])]])]])]

//# sourceMappingURL=splash.uvue.map