fn ad_run()
sss out=false
us(16,"visibility","gone")
us(9,"visibility","gone")
t()
{
  bfvs(2,sss.video)
  bfvss(2,"st")
  
  //循环监听是否成功开始播放
  w(true)
  {
    bfvss(2,"ip",is_bo)
    f(is_bo==true)
    {
      ufnsui()
      {
        //已开始播放,获取视频时长
        bfvss(2,"dn",all)
        //换算为秒
        s(all/1000,alltis)
        
        //
        
        //判断播放多长时间后可跳过
        f(sss.cantg==-1)
        {
          //不可跳过
          s overt=0
        }
        else f(sss.cantg==-2)
        {
          //一半
          s(alltis/2,o)
          s(alltis-o,overt)
        }
        else f(sss.cantg&lt;5)
        {
          s o=5
          s(alltis-o,overt)
        }
        else f(sss.cantg&gt;60)
        {
          s o=60
          s(alltis-o,overt)
        }
        else
        {
          s(alltis-sss.cantg,overt)
        }
        
        //循环开始倒计时
        us(7,"visibility",true)
        t()
        {
          w(true)
          {
            ufnsui()
            {
              us(8,"text",alltis)
            }
            f(alltis==0)
            {
              stop(1000)
              ufnsui()
              {
                sss over=true
                
                bfvss(2,"pe")
                us(6,"visibility","gone")
                us(16,"visibility","gone")
                us(9,"visibility","visible")
                
                //动画处理
                us(3,"visibility","visible")
                dha(dh, false, true)
                dh(dh, "duration", 500)
                us(3, "dh", dh)
                
                //设置数据
                us(15,"src",sss.td)
                us(24,"src",sss.icon)
                us(25,"text",sss.title)
                us(26,"text",sss.content)
                
              }
              break
            }
            f(alltis==overt)
            {
              ufnsui()
              {
                //us(6,"visibility","gone")
                us(16,"visibility","visible")
                //us(9,"visibility","visible")
              }
            }
            f(sss.out==true)
            {
              break
            }
            s-(1,alltis)
            stop(1000)
          }
        }
      }
      break
    }
  }
}
end fn