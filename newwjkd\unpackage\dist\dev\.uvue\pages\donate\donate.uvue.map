{"version": 3, "file": "pages/donate/donate.uvue", "names": [], "sources": ["pages/donate/donate.uvue"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 头部 -->\n    <view class=\"header\">\n      <text class=\"header-title\">捐赠支持</text>\n      <text class=\"header-subtitle\">您的支持是我们前进的动力</text>\n    </view>\n    \n    <!-- 捐赠说明 -->\n    <view class=\"donate-info\">\n      <view class=\"info-card\">\n        <text class=\"info-title\">为什么需要捐赠？</text>\n        <text class=\"info-content\">\n          文件快递服务需要服务器维护费用、域名费用等成本支出。您的捐赠将帮助我们：\n          • 维持服务器稳定运行\n          • 提升文件传输速度\n          • 开发更多实用功能\n          • 提供更好的用户体验\n        </text>\n      </view>\n    </view>\n    \n    <!-- 捐赠方式 -->\n    <view class=\"donate-methods\">\n      <text class=\"section-title\">选择捐赠方式</text>\n      \n      <view class=\"method-card\" @click=\"showQRCode('alipay')\">\n        <image class=\"method-icon\" src=\"/static/images/alipay-icon.png\" mode=\"aspectFit\"></image>\n        <view class=\"method-info\">\n          <text class=\"method-name\">支付宝</text>\n          <text class=\"method-desc\">扫码支付，安全便捷</text>\n        </view>\n        <text class=\"method-arrow\">></text>\n      </view>\n      \n      <view class=\"method-card\" @click=\"showQRCode('wechat')\">\n        <image class=\"method-icon\" src=\"/static/images/wechat-icon.png\" mode=\"aspectFit\"></image>\n        <view class=\"method-info\">\n          <text class=\"method-name\">微信支付</text>\n          <text class=\"method-desc\">微信扫码，快速支付</text>\n        </view>\n        <text class=\"method-arrow\">></text>\n      </view>\n    </view>\n    \n    <!-- 捐赠记录 -->\n    <view class=\"donate-records\" v-if=\"donateRecords.length > 0\">\n      <text class=\"section-title\">感谢以下用户的支持</text>\n      <scroll-view class=\"records-list\" scroll-y=\"true\">\n        <view class=\"record-item\" v-for=\"(record, index) in donateRecords\" :key=\"index\">\n          <text class=\"record-name\">{{ record.name }}</text>\n          <text class=\"record-amount\">¥{{ record.amount }}</text>\n          <text class=\"record-time\">{{ record.time }}</text>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 二维码对话框 -->\n    <view class=\"modal-overlay\" v-if=\"showQRDialog\" @click=\"hideQRDialog\">\n      <view class=\"modal-content\" @click.stop>\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">{{ currentPayMethod === 'alipay' ? '支付宝' : '微信' }}捐赠</text>\n        </view>\n        <view class=\"modal-body\">\n          <image class=\"qr-code\" :src=\"currentQRCode\" mode=\"aspectFit\"></image>\n          <text class=\"qr-desc\">请使用{{ currentPayMethod === 'alipay' ? '支付宝' : '微信' }}扫描二维码</text>\n        </view>\n        <view class=\"modal-actions\">\n          <button class=\"modal-btn\" @click=\"saveQRCode\">保存二维码</button>\n          <button class=\"modal-btn primary\" @click=\"hideQRDialog\">关闭</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getDonateInfo } from '@/utils/api.js'\nimport { showToast } from '@/utils/common.js'\n\nexport default {\n  data() {\n    return {\n      showQRDialog: false,\n      currentPayMethod: '',\n      currentQRCode: '',\n      donateRecords: [],\n      qrCodes: {\n        alipay: '/static/images/alipay-qr.png',\n        wechat: '/static/images/wechat-qr.png'\n      }\n    }\n  },\n  \n  onLoad() {\n    this.loadDonateInfo()\n  },\n  \n  methods: {\n    // 加载捐赠信息\n    async loadDonateInfo() {\n      try {\n        const result = await getDonateInfo()\n        if (result.status === 1) {\n          this.donateRecords = result.records || []\n          // 更新二维码地址\n          if (result.qrCodes) {\n            this.qrCodes = { ...this.qrCodes, ...result.qrCodes }\n          }\n        }\n      } catch (error) {\n        console.error('加载捐赠信息失败:', error)\n      }\n    },\n    \n    // 显示二维码\n    showQRCode(method) {\n      this.currentPayMethod = method\n      this.currentQRCode = this.qrCodes[method]\n      this.showQRDialog = true\n    },\n    \n    // 隐藏二维码对话框\n    hideQRDialog() {\n      this.showQRDialog = false\n      this.currentPayMethod = ''\n      this.currentQRCode = ''\n    },\n    \n    // 保存二维码\n    saveQRCode() {\n      // 这里实现保存二维码到相册的逻辑\n      uni.saveImageToPhotosAlbum({\n        filePath: this.currentQRCode,\n        success: () => {\n          showToast('二维码已保存到相册')\n        },\n        fail: () => {\n          showToast('保存失败，请手动截图保存')\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  min-height: 100vh;\n  background-color: #f8f8f8;\n}\n\n/* 头部 */\n.header {\n  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);\n  padding: 40px 20px 30px;\n  color: #ffffff;\n  text-align: center;\n}\n\n.header-title {\n  font-size: 24px;\n  font-weight: bold;\n  margin-bottom: 8px;\n}\n\n.header-subtitle {\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n/* 捐赠说明 */\n.donate-info {\n  padding: 20px;\n}\n\n.info-card {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.info-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 12px;\n}\n\n.info-content {\n  font-size: 14px;\n  color: #666666;\n  line-height: 1.6;\n}\n\n/* 捐赠方式 */\n.donate-methods {\n  padding: 0 20px 20px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 15px;\n}\n\n.method-card {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 12px;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  cursor: pointer;\n  transition: transform 0.2s ease;\n}\n\n.method-card:active {\n  transform: scale(0.98);\n}\n\n.method-icon {\n  width: 40px;\n  height: 40px;\n  margin-right: 15px;\n}\n\n.method-info {\n  flex: 1;\n}\n\n.method-name {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 4px;\n}\n\n.method-desc {\n  font-size: 12px;\n  color: #999999;\n}\n\n.method-arrow {\n  font-size: 18px;\n  color: #cccccc;\n  font-weight: bold;\n}\n\n/* 捐赠记录 */\n.donate-records {\n  padding: 0 20px 20px;\n}\n\n.records-list {\n  background-color: #ffffff;\n  border-radius: 12px;\n  max-height: 200px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.record-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 15px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.record-item:last-child {\n  border-bottom: none;\n}\n\n.record-name {\n  font-size: 14px;\n  color: #333333;\n  font-weight: 500;\n  flex: 1;\n}\n\n.record-amount {\n  font-size: 14px;\n  color: #1f999b;\n  font-weight: bold;\n  margin-right: 15px;\n}\n\n.record-time {\n  font-size: 12px;\n  color: #999999;\n}\n\n/* 模态框样式 */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background-color: #ffffff;\n  border-radius: 15px;\n  margin: 20px;\n  max-width: 320px;\n  width: 90%;\n  overflow: hidden;\n}\n\n.modal-header {\n  padding: 20px;\n  text-align: center;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.modal-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #333333;\n}\n\n.modal-body {\n  padding: 30px 20px;\n  text-align: center;\n}\n\n.qr-code {\n  width: 200px;\n  height: 200px;\n  margin-bottom: 15px;\n  border-radius: 8px;\n}\n\n.qr-desc {\n  font-size: 14px;\n  color: #666666;\n}\n\n.modal-actions {\n  display: flex;\n  border-top: 1px solid #f0f0f0;\n}\n\n.modal-btn {\n  flex: 1;\n  padding: 15px;\n  border: none;\n  background-color: #ffffff;\n  font-size: 16px;\n  font-weight: bold;\n  cursor: pointer;\n  border-right: 1px solid #f0f0f0;\n}\n\n.modal-btn:last-child {\n  border-right: none;\n}\n\n.modal-btn.primary {\n  color: #1f999b;\n}\n\n.modal-btn:active {\n  background-color: #f8f8f8;\n}\n</style>\n"], "mappings": ";AA6EA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAA<PERSON>;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;UACxC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtD;QACF;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC;IACF,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACzB,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACxB,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B;MACF,CAAC;IACH;EACF;AACF;;;;;;SA9IE,mBAwEO,mBAxED,KAAK,EAAC,WAAW;IAErB,mBAGO,mBAHD,KAAK,EAAC,QAAQ;MAClB,mBAAsC,mBAAhC,KAAK,EAAC,cAAc,KAAC,MAAI;MAC/B,mBAAiD,mBAA3C,KAAK,EAAC,iBAAiB,KAAC,cAAY;;IAI5C,mBAWO,mBAXD,KAAK,EAAC,aAAa;MACvB,mBASO,mBATD,KAAK,EAAC,WAAW;QACrB,mBAAwC,mBAAlC,KAAK,EAAC,YAAY,KAAC,UAAQ;QACjC,mBAMO,mBAND,KAAK,EAAC,cAAc,KAAC,sFAM3B;;;IAKJ,mBAoBO,mBApBD,KAAK,EAAC,gBAAgB;MAC1B,mBAAyC,mBAAnC,KAAK,EAAC,eAAe,KAAC,QAAM;MAElC,mBAOO;QAPD,KAAK,EAAC,aAAa;QAAE,OAAK,SAAE,eAAU;;QAC1C,mBAAyF;UAAlF,KAAK,EAAC,aAAa;UAAC,GAAG,EAAC,gCAAgC;UAAC,IAAI,EAAC,WAAW;;QAChF,mBAGO,mBAHD,KAAK,EAAC,aAAa;UACvB,mBAAoC,mBAA9B,KAAK,EAAC,aAAa,KAAC,KAAG;UAC7B,mBAA0C,mBAApC,KAAK,EAAC,aAAa,KAAC,WAAS;;QAErC,mBAAmC,mBAA7B,KAAK,EAAC,cAAc,KAAC,GAAC;;MAG9B,mBAOO;QAPD,KAAK,EAAC,aAAa;QAAE,OAAK,SAAE,eAAU;;QAC1C,mBAAyF;UAAlF,KAAK,EAAC,aAAa;UAAC,GAAG,EAAC,gCAAgC;UAAC,IAAI,EAAC,WAAW;;QAChF,mBAGO,mBAHD,KAAK,EAAC,aAAa;UACvB,mBAAqC,mBAA/B,KAAK,EAAC,aAAa,KAAC,MAAI;UAC9B,mBAA0C,mBAApC,KAAK,EAAC,aAAa,KAAC,WAAS;;QAErC,mBAAmC,mBAA7B,KAAK,EAAC,cAAc,KAAC,GAAC;;;IAKG,kBAAa,CAAC,MAAM;QAAvD,mBASO;;UATD,KAAK,EAAC,gBAAgB;;UAC1B,mBAA4C,mBAAtC,KAAK,EAAC,eAAe,KAAC,WAAS;UACrC,mBAMc;YAND,KAAK,EAAC,cAAc;YAAC,UAAQ,EAAC,MAAM;;YAC/C,mBAIO,yCAJ6C,kBAAa,GAA/B,MAAM,EAAE,KAAK,EAAb,OAAM;qBAAxC,mBAIO;gBAJD,KAAK,EAAC,aAAa;gBAA2C,GAAG,EAAE,KAAK;;gBAC5E,mBAAkD,mBAA5C,KAAK,EAAC,aAAa,qBAAI,MAAM,CAAC,IAAI;gBACxC,mBAAuD,mBAAjD,KAAK,EAAC,eAAe,KAAC,GAAC,mBAAG,MAAM,CAAC,MAAM;gBAC7C,mBAAkD,mBAA5C,KAAK,EAAC,aAAa,qBAAI,MAAM,CAAC,IAAI;;;;;;WAMZ,iBAAY;QAA9C,mBAcO;;UAdD,KAAK,EAAC,eAAe;UAAsB,OAAK,EAAE,iBAAY;;UAClE,mBAYO;YAZD,KAAK,EAAC,eAAe;YAAE,OAAK,gBAAN,QAAW;;YACrC,mBAEO,mBAFD,KAAK,EAAC,cAAc;cACxB,mBAAqF,mBAA/E,KAAK,EAAC,aAAa,qBAAI,qBAAgB,gCAA+B,IAAE;;YAEhF,mBAGO,mBAHD,KAAK,EAAC,YAAY;cACtB,mBAAqE;gBAA9D,KAAK,EAAC,SAAS;gBAAE,GAAG,EAAE,kBAAa;gBAAE,IAAI,EAAC,WAAW;;cAC5D,mBAAuF,mBAAjF,KAAK,EAAC,SAAS,KAAC,KAAG,mBAAG,qBAAgB,gCAA+B,OAAK;;YAElF,mBAGO,mBAHD,KAAK,EAAC,eAAe;cACzB,mBAA4D;gBAApD,KAAK,EAAC,WAAW;gBAAE,OAAK,EAAE,eAAU;kBAAE,OAAK;cACnD,mBAAmE;gBAA3D,KAAK,EAAC,mBAAmB;gBAAE,OAAK,EAAE,iBAAY;kBAAE,IAAE"}