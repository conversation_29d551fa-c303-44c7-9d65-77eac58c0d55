
import { getFile, uploadFile, downloadFile } from '@/utils/api.js'
import { saveUploadRecord, getNoAdStatus } from '@/utils/storage.js'
import { showToast, showLoading, hideLoading, showConfirm, validatePickupCode, copyToClipboard } from '@/utils/common.js'

const __sfc__ = defineComponent({
  data() {
    return {
      // 对话框显示状态
      showPickup: false,
      showSend: false,
      showDownloadProgress: false,
      showUploadResult: false,
      showLoading: false,

      // 取件相关
      pickupCode: '',

      // 下载进度
      downloadProgress: 0,

      // 上传结果
      uploadResultData: {
        msg: '',
        url: '',
        gcode: '',
        qrCode: ''
      },

      // 加载文本
      loadingText: '请稍后...'
    }
  },

  computed: {
    isValidPickupCode() {
      return validatePickupCode(this.pickupCode)
    }
  },

  onLoad() {
    this.checkForUpdates()
    this.checkDonateStatus()
  },

  methods: {
    // 显示取件对话框
    showPickupDialog() {
      this.showPickup = true
      this.pickupCode = ''
    },

    // 隐藏取件对话框
    hidePickupDialog() {
      this.showPickup = false
      this.pickupCode = ''
    },

    // 显示发件对话框
    showSendDialog() {
      // 检查免广告状态
      const noAd = getNoAdStatus()
      if (!noAd) {
        // 这里可以显示广告
        console.log('显示广告', " at pages/index/index.uvue:196")
      }
      this.showSend = true
    },

    // 隐藏发件对话框
    hideSendDialog() {
      this.showSend = false
    },

    // 获取文件
    async getFile() {
      if (!this.isValidPickupCode) {
        showToast('请输入完整的8位取件码')
        return
      }

      this.showLoading = true
      this.loadingText = '请稍后'

      try {
        const result = await getFile(this.pickupCode)

        if (result.status === 1) {
          // 获取成功，开始下载
          const downloadUrl = `https://file.8845.top${result.durl}`
          const fileName = result.wjm

          this.hidePickupDialog()
          this.showLoading = false
          this.showDownloadProgress = true
          this.downloadProgress = 0

          await this.downloadFileWithProgress(downloadUrl, fileName)
        } else {
          showToast(result.msg || '取件失败')
        }
      } catch (error) {
        console.error('获取文件失败:', error, " at pages/index/index.uvue:234")
        showToast('连接超时')
      } finally {
        this.showLoading = false
      }
    },

    // 下载文件并显示进度
    async downloadFileWithProgress(url, fileName) {
      try {
        // 模拟下载进度
        const progressInterval = setInterval(() => {
          if (this.downloadProgress < 90) {
            this.downloadProgress += Math.random() * 10
          } else if (this.downloadProgress < 100) {
            this.downloadProgress += 1
          } else {
            clearInterval(progressInterval)
          }
        }, 200)

        // 实际下载文件
        const filePath = await downloadFile(url, fileName)

        clearInterval(progressInterval)
        this.downloadProgress = 100

        setTimeout(() => {
          this.showDownloadProgress = false
          showToast('下载成功 已保存至 根目录/下载/文件快递/')
        }, 500)

      } catch (error) {
        console.error('下载失败:', error, " at pages/index/index.uvue:267")
        this.showDownloadProgress = false
        showToast('下载失败')
      }
    },

    // 打开自制文件管理器
    openCustomFileManager() {
      this.hideSendDialog()
      uni.navigateTo({
        url: '/pages/fileManager/fileManager'
      })
    },

    // 打开系统文件管理器
    openSystemFileManager() {
      this.hideSendDialog()

      // 在uni-app x中使用不同的文件选择API














      uni.chooseImage({
        count: 1,
        sourceType: ['album'],
        success: (res) => {
          if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            const filePath = res.tempFilePaths[0]
            const fileName = filePath.split('/').pop() || 'unknown'
            this.uploadSelectedFile(filePath, fileName)
          }
        },
        fail: (error) => {
          console.error('选择文件失败:', error, " at pages/index/index.uvue:311")
          showToast('选择文件失败，请使用自制文件管理器')
        }
      })

    },

    // 上传选中的文件
    async uploadSelectedFile(file, fileName) {
      const confirmed = await showConfirm(`是否上传文件：${fileName}？`)
      if (!confirmed) return

      this.showLoading = true
      this.loadingText = '上传中'

      try {
        // 处理不同类型的文件输入
        let filePath = file
        if (typeof file === 'object' && file.path) {
          filePath = file.path
        } else if (typeof file === 'object' && file instanceof File) {
          // Web环境下的File对象
          filePath = file
        }

        const result = await uploadFile(filePath)

        if (result.status === 1) {
          // 上传成功
          const gcode = result.get_code
          const fileUrl = `https://file.8845.top/file/${gcode}/${fileName}`

          // 生成二维码（这里使用简单的二维码API）
          const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${UTSAndroid.consoleDebugError(encodeURIComponent(fileUrl), " at pages/index/index.uvue:344")}`

          this.uploadResultData = {
            msg: result.msg,
            url: fileUrl,
            gcode: gcode,
            qrCode: qrCodeUrl
          }

          // 保存上传记录
          saveUploadRecord({
            filename: fileName,
            gcode: gcode,
            url: fileUrl
          })

          this.showLoading = false
          this.showUploadResult = true

        } else {
          showToast(result.msg || '上传失败')
        }
      } catch (error) {
        console.error('上传失败:', error, " at pages/index/index.uvue:367")
        showToast('连接超时')
      } finally {
        this.showLoading = false
      }
    },

    // 隐藏上传结果对话框
    hideUploadResult() {
      this.showUploadResult = false
    },

    // 复制URL
    async copyUrl() {
      await copyToClipboard(this.uploadResultData.url)
    },

    // 复制取件码
    async copyCode() {
      await copyToClipboard(this.uploadResultData.gcode)
    },

    // 保存二维码
    saveQRCode() {
      // 这里实现保存二维码的逻辑
      showToast('已保存至 根目录/下载/文件快递/二维码/')
    },

    // 显示捐赠页面
    showDonate() {
      uni.navigateTo({
        url: '/pages/donate/donate'
      })
    },

    // 显示上传记录
    showUploadRecord() {
      uni.navigateTo({
        url: '/pages/uploadRecord/uploadRecord'
      })
    },

    // 检查更新
    async checkForUpdates() {
      // 这里实现检查更新的逻辑
      console.log('检查更新', " at pages/index/index.uvue:412")
    },

    // 检查捐赠状态
    async checkDonateStatus() {
      // 这里实现检查捐赠状态的逻辑
      console.log('检查捐赠状态', " at pages/index/index.uvue:418")
    }
  }
})

export default __sfc__
function GenPagesIndexIndexRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
  return createElementVNode("view", utsMapOf({ class: "container" }), [
    createElementVNode("view", utsMapOf({ class: "header" }), [
      createElementVNode("image", utsMapOf({
        class: "logo",
        src: "/static/images/logo.png",
        mode: "aspectFit"
      })),
      createElementVNode("text", utsMapOf({ class: "app-title" }), "文件快递服务")
    ]),
    createElementVNode("view", utsMapOf({ class: "main-content" }), [
      createElementVNode("view", utsMapOf({
        class: "function-card",
        onClick: _ctx.showPickupDialog
      }), [
        createElementVNode("view", utsMapOf({ class: "card-content" }), [
          createElementVNode("text", utsMapOf({ class: "card-title" }), "取件")
        ])
      ], 8 /* PROPS */, ["onClick"]),
      createElementVNode("view", utsMapOf({
        class: "function-card send-card",
        onClick: _ctx.showSendDialog
      }), [
        createElementVNode("view", utsMapOf({ class: "card-content" }), [
          createElementVNode("text", utsMapOf({ class: "card-title send-title" }), "发件")
        ])
      ], 8 /* PROPS */, ["onClick"])
    ]),
    createElementVNode("view", utsMapOf({ class: "bottom-actions" }), [
      createElementVNode("view", utsMapOf({
        class: "action-item",
        onClick: _ctx.showDonate
      }), [
        createElementVNode("text", utsMapOf({ class: "action-text" }), "捐赠App"),
        createElementVNode("view", utsMapOf({ class: "action-line" }))
      ], 8 /* PROPS */, ["onClick"]),
      createElementVNode("view", utsMapOf({
        class: "action-item",
        onClick: _ctx.showUploadRecord
      }), [
        createElementVNode("text", utsMapOf({ class: "action-text" }), "上传记录"),
        createElementVNode("view", utsMapOf({ class: "action-line" }))
      ], 8 /* PROPS */, ["onClick"])
    ]),
    isTrue(_ctx.showPickup)
      ? createElementVNode("view", utsMapOf({
          key: 0,
          class: "modal-overlay",
          onClick: _ctx.hidePickupDialog
        }), [
          createElementVNode("view", utsMapOf({
            class: "modal-content",
            onClick: withModifiers(() => {}, ["stop"])
          }), [
            createElementVNode("view", utsMapOf({ class: "modal-header" }), [
              createElementVNode("image", utsMapOf({
                class: "modal-icon",
                src: "/static/images/pickup-icon.png",
                mode: "aspectFit"
              }))
            ]),
            createElementVNode("view", utsMapOf({ class: "modal-body" }), [
              createElementVNode("view", utsMapOf({ class: "input-container" }), [
                createElementVNode("input", utsMapOf({
                  class: "pickup-input",
                  modelValue: _ctx.pickupCode,
                  onInput: ($event: InputEvent) => {(_ctx.pickupCode) = $event.detail.value},
                  placeholder: "请输入8位取件码",
                  maxlength: "8",
                  type: "number"
                }), null, 40 /* PROPS, NEED_HYDRATION */, ["modelValue", "onInput"])
              ]),
              createElementVNode("view", utsMapOf({ class: "modal-actions" }), [
                createElementVNode("button", utsMapOf({
                  class: "modal-btn get-btn",
                  onClick: _ctx.getFile,
                  disabled: !_ctx.isValidPickupCode
                }), " 获取 ", 8 /* PROPS */, ["onClick", "disabled"])
              ])
            ])
          ], 8 /* PROPS */, ["onClick"])
        ], 8 /* PROPS */, ["onClick"])
      : createCommentVNode("v-if", true),
    isTrue(_ctx.showSend)
      ? createElementVNode("view", utsMapOf({
          key: 1,
          class: "modal-overlay",
          onClick: _ctx.hideSendDialog
        }), [
          createElementVNode("view", utsMapOf({
            class: "modal-content",
            onClick: withModifiers(() => {}, ["stop"])
          }), [
            createElementVNode("view", utsMapOf({ class: "modal-header" }), [
              createElementVNode("image", utsMapOf({
                class: "modal-icon",
                src: "/static/images/send-icon.png",
                mode: "aspectFit"
              }))
            ]),
            createElementVNode("view", utsMapOf({ class: "modal-body" }), [
              createElementVNode("text", utsMapOf({ class: "modal-title" }), "选择文件"),
              createElementVNode("view", utsMapOf({ class: "file-options" }), [
                createElementVNode("button", utsMapOf({
                  class: "file-option-btn",
                  onClick: _ctx.openCustomFileManager
                }), " 自制文件管理器 ", 8 /* PROPS */, ["onClick"]),
                createElementVNode("button", utsMapOf({
                  class: "file-option-btn primary",
                  onClick: _ctx.openSystemFileManager
                }), " 系统文件管理器 ", 8 /* PROPS */, ["onClick"])
              ])
            ])
          ], 8 /* PROPS */, ["onClick"])
        ], 8 /* PROPS */, ["onClick"])
      : createCommentVNode("v-if", true),
    isTrue(_ctx.showDownloadProgress)
      ? createElementVNode("view", utsMapOf({
          key: 2,
          class: "modal-overlay",
          onClick: withModifiers(() => {}, ["stop"])
        }), [
          createElementVNode("view", utsMapOf({ class: "modal-content" }), [
            createElementVNode("view", utsMapOf({ class: "progress-header" }), [
              createElementVNode("text", utsMapOf({ class: "progress-title" }), "正在下载")
            ]),
            createElementVNode("view", utsMapOf({ class: "progress-body" }), [
              createElementVNode("view", utsMapOf({ class: "progress-bar" }), [
                createElementVNode("view", utsMapOf({
                  class: "progress-fill",
                  style: normalizeStyle(utsMapOf({ width: _ctx.downloadProgress + '%' }))
                }), null, 4 /* STYLE */)
              ]),
              createElementVNode("text", utsMapOf({ class: "progress-text" }), toDisplayString(_ctx.downloadProgress) + "%", 1 /* TEXT */)
            ])
          ])
        ], 8 /* PROPS */, ["onClick"])
      : createCommentVNode("v-if", true),
    isTrue(_ctx.showUploadResult)
      ? createElementVNode("view", utsMapOf({
          key: 3,
          class: "modal-overlay",
          onClick: _ctx.hideUploadResult
        }), [
          createElementVNode("view", utsMapOf({
            class: "modal-content upload-result",
            onClick: withModifiers(() => {}, ["stop"])
          }), [
            createElementVNode("view", utsMapOf({ class: "result-header" }), [
              createElementVNode("text", utsMapOf({ class: "result-title" }), toDisplayString(_ctx.uploadResultData.msg), 1 /* TEXT */)
            ]),
            createElementVNode("view", utsMapOf({ class: "result-body" }), [
              createElementVNode("text", utsMapOf({ class: "result-label" }), "文件地址："),
              createElementVNode("text", utsMapOf({
                class: "result-url",
                onClick: _ctx.copyUrl
              }), toDisplayString(_ctx.uploadResultData.url), 9 /* TEXT, PROPS */, ["onClick"]),
              createElementVNode("text", utsMapOf({ class: "result-label" }), "取件码："),
              createElementVNode("text", utsMapOf({
                class: "result-code",
                onClick: _ctx.copyCode
              }), toDisplayString(_ctx.uploadResultData.gcode), 9 /* TEXT, PROPS */, ["onClick"]),
              createElementVNode("text", utsMapOf({ class: "result-label" }), "文件二维码："),
              createElementVNode("image", utsMapOf({
                class: "qr-code",
                src: _ctx.uploadResultData.qrCode,
                mode: "aspectFit"
              }), null, 8 /* PROPS */, ["src"])
            ]),
            createElementVNode("view", utsMapOf({ class: "result-actions" }), [
              createElementVNode("button", utsMapOf({
                class: "result-btn",
                onClick: _ctx.saveQRCode
              }), "保存二维码", 8 /* PROPS */, ["onClick"]),
              createElementVNode("button", utsMapOf({
                class: "result-btn",
                onClick: _ctx.copyCode
              }), "复制取件码", 8 /* PROPS */, ["onClick"]),
              createElementVNode("button", utsMapOf({
                class: "result-btn primary",
                onClick: _ctx.hideUploadResult
              }), "确定", 8 /* PROPS */, ["onClick"])
            ])
          ], 8 /* PROPS */, ["onClick"])
        ], 8 /* PROPS */, ["onClick"])
      : createCommentVNode("v-if", true),
    isTrue(_ctx.showLoading)
      ? createElementVNode("view", utsMapOf({
          key: 4,
          class: "modal-overlay",
          onClick: withModifiers(() => {}, ["stop"])
        }), [
          createElementVNode("view", utsMapOf({ class: "loading-modal" }), [
            createElementVNode("view", utsMapOf({ class: "loading-spinner" })),
            createElementVNode("text", utsMapOf({ class: "loading-text" }), toDisplayString(_ctx.loadingText), 1 /* TEXT */)
          ])
        ], 8 /* PROPS */, ["onClick"])
      : createCommentVNode("v-if", true)
  ])
}
const GenPagesIndexIndexStyles = [utsMapOf([["container", padStyleMapOf(utsMapOf([["backgroundColor", "#ffffff"], ["display", "flex"], ["flexDirection", "column"]]))], ["header", padStyleMapOf(utsMapOf([["display", "flex"], ["flexDirection", "column"], ["alignItems", "center"], ["paddingTop", 60], ["paddingRight", 20], ["paddingBottom", 40], ["paddingLeft", 20], ["backgroundImage", "linear-gradient(135deg, #1f999b 0%, #16a085 100%)"]]))], ["logo", padStyleMapOf(utsMapOf([["width", 80], ["height", 80], ["marginBottom", 20], ["borderRadius", 15]]))], ["app-title", padStyleMapOf(utsMapOf([["fontSize", 20], ["fontWeight", "bold"], ["color", "#ffffff"], ["textShadow", "0 2px 4px rgba(0, 0, 0, 0.2)"]]))], ["main-content", padStyleMapOf(utsMapOf([["flex", 1], ["paddingTop", 40], ["paddingRight", 20], ["paddingBottom", 40], ["paddingLeft", 20], ["display", "flex"], ["flexDirection", "column"], ["gap", "20px"]]))], ["function-card", padStyleMapOf(utsMapOf([["backgroundColor", "#ffffff"], ["borderRadius", 15], ["paddingTop", 30], ["paddingRight", 30], ["paddingBottom", 30], ["paddingLeft", 30], ["boxShadow", "0 4px 12px rgba(0, 0, 0, 0.1)"], ["borderWidth", 2], ["borderStyle", "solid"], ["borderColor", "#f0f0f0"], ["transitionDuration", "0.3s"], ["transitionTimingFunction", "ease"], ["transform:active", "scale(0.98)"], ["boxShadow:active", "0 2px 8px rgba(0, 0, 0, 0.15)"]]))], ["send-card", padStyleMapOf(utsMapOf([["backgroundImage", "linear-gradient(135deg, #1f999b 0%, #16a085 100%)"], ["borderWidth", "medium"], ["borderStyle", "none"], ["borderColor", "#000000"]]))], ["card-content", padStyleMapOf(utsMapOf([["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"]]))], ["card-title", padStyleMapOf(utsMapOf([["fontSize", 18], ["fontWeight", "bold"], ["color", "#1f999b"]]))], ["send-title", padStyleMapOf(utsMapOf([["color", "#ffffff"]]))], ["bottom-actions", padStyleMapOf(utsMapOf([["display", "flex"], ["justifyContent", "space-around"], ["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20], ["marginTop", "auto"]]))], ["action-item", padStyleMapOf(utsMapOf([["display", "flex"], ["flexDirection", "column"], ["alignItems", "center"], ["cursor", "pointer"]]))], ["action-text", padStyleMapOf(utsMapOf([["fontSize", 13], ["fontWeight", "bold"], ["color", "#b8b8b8"], ["marginBottom", 5]]))], ["action-line", padStyleMapOf(utsMapOf([["width", 50], ["height", 2], ["backgroundColor", "#b8b8b8"]]))], ["modal-overlay", padStyleMapOf(utsMapOf([["position", "fixed"], ["top", 0], ["left", 0], ["right", 0], ["bottom", 0], ["backgroundColor", "rgba(59,0,0,0.6)"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["zIndex", 1000]]))], ["modal-content", padStyleMapOf(utsMapOf([["backgroundColor", "#ffffff"], ["borderRadius", 15], ["marginTop", 20], ["marginRight", 20], ["marginBottom", 20], ["marginLeft", 20], ["width", 320], ["overflow", "hidden"], ["boxShadow", "0 8px 24px rgba(0, 0, 0, 0.2)"]]))], ["modal-header", padStyleMapOf(utsMapOf([["display", "flex"], ["justifyContent", "center"], ["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 10], ["paddingLeft", 20]]))], ["modal-icon", padStyleMapOf(utsMapOf([["width", 80], ["height", 80]]))], ["modal-body", padStyleMapOf(utsMapOf([["paddingTop", 0], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20]]))], ["modal-title", padStyleMapOf(utsMapOf([["fontSize", 17], ["fontWeight", "bold"], ["color", "#1f999b"], ["textAlign", "center"], ["marginBottom", 20]]))], ["input-container", padStyleMapOf(utsMapOf([["marginBottom", 20]]))], ["pickup-input", padStyleMapOf(utsMapOf([["width", "100%"], ["height", 45], ["borderWidth", "medium"], ["borderStyle", "none"], ["borderColor", "#000000"], ["backgroundColor", "#f8f8f8"], ["borderRadius", 10], ["paddingTop", 0], ["paddingRight", 15], ["paddingBottom", 0], ["paddingLeft", 15], ["fontSize", 15], ["textAlign", "center"], ["color", "#1f999b"], ["color::placeholder", "#bcbcbc"]]))], ["modal-actions", padStyleMapOf(utsMapOf([["display", "flex"], ["justifyContent", "center"]]))], ["modal-btn", padStyleMapOf(utsMapOf([["backgroundImage", "linear-gradient(135deg, #1f999b 0%, #16a085 100%)"], ["color", "#ffffff"], ["borderWidth", "medium"], ["borderStyle", "none"], ["borderColor", "#000000"], ["borderRadius", 10], ["paddingTop", 12], ["paddingRight", 30], ["paddingBottom", 12], ["paddingLeft", 30], ["fontSize", 15], ["fontWeight", "bold"], ["cursor", "pointer"], ["backgroundColor:disabled", "#cccccc"], ["cursor:disabled", "not-allowed"]]))], ["file-options", padStyleMapOf(utsMapOf([["display", "flex"], ["flexDirection", "column"], ["gap", "15px"]]))], ["file-option-btn", utsMapOf([["", utsMapOf([["backgroundColor", "#ffffff"], ["borderWidth", 2], ["borderStyle", "solid"], ["borderColor", "#1f999b"], ["color", "#1f999b"], ["borderRadius", 10], ["paddingTop", 12], ["paddingRight", 20], ["paddingBottom", 12], ["paddingLeft", 20], ["fontSize", 15], ["fontWeight", "bold"], ["cursor", "pointer"]])], [".primary", utsMapOf([["backgroundImage", "linear-gradient(135deg, #1f999b 0%, #16a085 100%)"], ["color", "#ffffff"], ["borderWidth", "medium"], ["borderStyle", "none"], ["borderColor", "#000000"]])]])], ["progress-header", padStyleMapOf(utsMapOf([["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20], ["textAlign", "center"]]))], ["progress-title", padStyleMapOf(utsMapOf([["fontSize", 17], ["fontWeight", "bold"], ["color", "#1f999b"]]))], ["progress-body", padStyleMapOf(utsMapOf([["paddingTop", 0], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20], ["display", "flex"], ["flexDirection", "column"], ["alignItems", "center"]]))], ["progress-bar", padStyleMapOf(utsMapOf([["width", "100%"], ["height", 6], ["backgroundColor", "#f0f0f0"], ["borderRadius", 3], ["overflow", "hidden"], ["marginBottom", 10]]))], ["progress-fill", padStyleMapOf(utsMapOf([["height", "100%"], ["backgroundImage", "linear-gradient(90deg, #1f999b 0%, #16a085 100%)"], ["borderRadius", 3], ["transitionProperty", "width"], ["transitionDuration", "0.3s"], ["transitionTimingFunction", "ease"]]))], ["progress-text", padStyleMapOf(utsMapOf([["fontSize", 12], ["color", "#999999"]]))], ["upload-result", padStyleMapOf(utsMapOf([["overflowY", "auto"]]))], ["result-header", padStyleMapOf(utsMapOf([["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20], ["textAlign", "center"], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#f0f0f0"]]))], ["result-title", padStyleMapOf(utsMapOf([["fontSize", 17], ["fontWeight", "bold"], ["color", "#1f999b"]]))], ["result-body", padStyleMapOf(utsMapOf([["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20]]))], ["result-label", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#1f999b"], ["fontWeight", "bold"], ["marginTop", 15], ["marginBottom", 5], ["marginTop:first-child", 0]]))], ["result-url", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#f98b8b"], ["fontWeight", "bold"], ["wordBreak", "break-all"], ["cursor", "pointer"], ["paddingTop", 8], ["paddingRight", 8], ["paddingBottom", 8], ["paddingLeft", 8], ["backgroundColor", "#f8f8f8"], ["borderRadius", 5]]))], ["result-code", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#f98b8b"], ["fontWeight", "bold"], ["wordBreak", "break-all"], ["cursor", "pointer"], ["paddingTop", 8], ["paddingRight", 8], ["paddingBottom", 8], ["paddingLeft", 8], ["backgroundColor", "#f8f8f8"], ["borderRadius", 5]]))], ["qr-code", padStyleMapOf(utsMapOf([["width", 120], ["height", 120], ["marginTop", 10], ["marginRight", 0], ["marginBottom", 10], ["marginLeft", 0], ["borderRadius", 5]]))], ["result-actions", padStyleMapOf(utsMapOf([["display", "flex"], ["justifyContent", "space-around"], ["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20], ["borderTopWidth", 1], ["borderTopStyle", "solid"], ["borderTopColor", "#f0f0f0"]]))], ["result-btn", utsMapOf([["", utsMapOf([["backgroundColor", "#ffffff"], ["borderWidth", 1], ["borderStyle", "solid"], ["borderColor", "#1f999b"], ["color", "#1f999b"], ["borderRadius", 8], ["paddingTop", 8], ["paddingRight", 12], ["paddingBottom", 8], ["paddingLeft", 12], ["fontSize", 12], ["fontWeight", "bold"], ["cursor", "pointer"], ["flex", 1], ["marginTop", 0], ["marginRight", 5], ["marginBottom", 0], ["marginLeft", 5]])], [".primary", utsMapOf([["backgroundImage", "linear-gradient(135deg, #1f999b 0%, #16a085 100%)"], ["color", "#ffffff"], ["borderWidth", "medium"], ["borderStyle", "none"], ["borderColor", "#000000"]])]])], ["loading-modal", padStyleMapOf(utsMapOf([["backgroundColor", "#ffffff"], ["borderRadius", 10], ["paddingTop", 30], ["paddingRight", 30], ["paddingBottom", 30], ["paddingLeft", 30], ["display", "flex"], ["flexDirection", "column"], ["alignItems", "center"], ["minWidth", 150]]))], ["loading-spinner", padStyleMapOf(utsMapOf([["width", 30], ["height", 30], ["borderWidth", 3], ["borderStyle", "solid"], ["borderColor", "#f3f3f3"], ["borderTopWidth", 3], ["borderTopStyle", "solid"], ["borderTopColor", "#1f999b"], ["animation", "spin 1s linear infinite"], ["marginBottom", 15]]))], ["loading-text", padStyleMapOf(utsMapOf([["fontSize", 15], ["color", "#1f999b"], ["fontWeight", "bold"]]))], ["@FONT-FACE", utsMapOf([["0", utsMapOf([])]])], ["@TRANSITION", utsMapOf([["function-card", utsMapOf([["duration", "0.3s"], ["timingFunction", "ease"]])], ["progress-fill", utsMapOf([["property", "width"], ["duration", "0.3s"], ["timingFunction", "ease"]])]])]])]

//# sourceMappingURL=index.uvue.map