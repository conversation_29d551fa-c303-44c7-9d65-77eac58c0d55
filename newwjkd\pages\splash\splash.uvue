<template>
  <view class="splash-container">
    <view class="content">
      <image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
      <text class="app-name">文件快递服务</text>
      <text class="version">v{{ version }}</text>
    </view>
    
    <view class="loading">
      <text class="loading-text">正在启动...</text>
    </view>
  </view>
</template>

<script>
import { getNoAdStatus } from '@/utils/storage.js'

export default {
  data() {
    return {
      version: '2.1'
    }
  },
  
  onLoad() {
    this.initApp()
  },
  
  methods: {
    async initApp() {
      try {
        // 检查免广告状态
        const noAd = getNoAdStatus()
        
        // 模拟启动延迟
        await this.delay(2000)
        
        // 根据广告状态决定跳转页面
        if (noAd) {
          // 直接跳转到主页
          uni.reLaunch({
            url: '/pages/index/index'
          })
        } else {
          // 跳转到加载页（可以在这里显示广告）
          uni.reLaunch({
            url: '/pages/index/index'
          })
        }
      } catch (error) {
        console.error('启动初始化失败:', error)
        // 出错时直接跳转到主页
        uni.reLaunch({
          url: '/pages/index/index'
        })
      }
    },
    
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style scoped>
.splash-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 100px;
}

.logo {
  width: 120px;
  height: 120px;
  margin-bottom: 30px;
  border-radius: 20px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.app-name {
  font-size: 28px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.version {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
}

.loading {
  position: absolute;
  bottom: 80px;
  display: flex;
  align-items: center;
}

.loading-text {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin-left: 10px;
}

/* 添加一些动画效果 */
.logo {
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.loading-text {
  animation: textBlink 1.5s ease-in-out infinite;
}

@keyframes textBlink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
