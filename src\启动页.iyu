<View id="1" did="0" type="RelativeLayout">
<ppt>width=-1
height=-1
background=white</ppt>
<event></event>
</View>
<View id="2" did="1" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center</ppt>
<event></event>
</View>
<View id="3" did="2" type="ImageView">
<ppt>width=100dp
height=300dp
src=@2.png</ppt>
<event></event>
</View>
<View id="6" did="2" type="LinearLayout">
<ppt>width=-2
height=-2
orientation=horizontal</ppt>
<event></event>
</View>
<View id="4" did="6" type="TextView">
<ppt>width=-2
height=-2
text=文件
textColor=#ff1f999b
textSize=25sp
textStyle=bold</ppt>
<event></event>
</View>
<View id="7" did="6" type="TextView">
<ppt>width=-2
height=-2
text=快递
textColor=#ff1f999b
textSize=20sp</ppt>
<event></event>
</View>
<View id="9" did="2" type="TextView">
<ppt>width=-2
height=-2
text=</ppt>
<event></event>
</View>
<UIEventset><eventItme type="loading">
uycl("#3b000000",true)

ss tcxs=0
//fn fun.tcxs(1,"正在连接服务器")
t()
{
  stop(2000)
  hs("https://file.8845.top/connect.php",re)
  syso(re)
  f(re==)
  {
    ufnsui()
    {
      tw("服务器连接失败")
    }
  }
  else
  {
    ufnsui()
    {
      fn codel.解密(re)
      syso(sss.原文)
      json(sss.原文,r)
      json(r,"get","status",status)
      f(status==1)
      {
        //fn fun.tcyc(1)
        ufnsui()
        {       
          uigo("主页.iyu")
          end()
        }
      }
    }
  }
}</eventItme></UIEventset>