//加载弹窗开启
fn tcxs(id,nr)
f(ss.tcxs==0)
{
  nvw(10000,id,"线性布局","width=-1\nheight=-1\norientation=vertical\nbackground=#3b000000\ngravity=center")
  nvw(10001,10000,"卡片","width=-1\nheight=-2\nlayout_marginLeft=20dp\nlayout_marginRight=20dp\napp_CardcornerRadius=10dp\napp_CardElevation=0")
  nvw(10002,10001,"线性布局","width=-1\nheight=-1\norientation=horizontal\ngravity=center_vertical\nlayout_marginLeft=10dp\nlayout_marginRight=10dp\nlayout_marginTop=10dp\nlayout_marginBottom=10dp")
  nvw(10004,10002,"进度条","width=30dp\nheight=30dp\nstyle=16842873\nlayout_marginTop=20dp\nlayout_marginBottom=20dp\nlayout_marginLeft=20dp")
  nvw(10003,10002,"文本","width=-2\nheight=-2\ntext=加载中\ntextSize=13sp\nlayout_marginLeft=20dp\ntextColor=#ff1f999b\ntextStyle=bold")
}
ssj(10000,"clicki")
{
  syso("")
}
fn 着色.控件着色(10004,"#ff1f999b")
us(10000,"visibility","gone")
us(10003,"text",nr)
t()
{
  stop(100)
  ufnsui()
  {
    us(10000,"visibility",true)
    dha(dh, false,true)
    dh(dh, "duration", 500)
    us(10000, "dh", dh)
  }
}
end fn

//加载弹窗关闭
fn tcyc(id)
t()
{
  stop(100)
  ufnsui()
  {
    dha(dh, true,false)
    dh(dh, "duration", 500)
    us(10000, "dh", dh)
    us(10000,"visibility","gone")
    ss tcxs==1
  }
}
end fn

fn xs(id)
us(id,"visibility",true)
dha(dh, false,true)
dh(dh, "duration", 500)
us(id, "dh", dh)
end fn

fn yc(id)
dha(dh, true,false)
dh(dh, "duration", 500)
us(id, "dh", dh)
us(id,"visibility","gone")
end fn

fn file_glq(path)
t()
{
  fl(path,lj)
  for(c;lj)
  {
    ss(path+"/"+c,ljs)
    fi(ljs,ilj)
    f(ilj==true)
    {
      s tp="@folder-2-1.png"
    }
    f(ilj==false)
    {
      s tp="@document-1.png"
    }
    ula(list,1=null,4=tp,7=c)
  }
  ufnsui()
  {
    
    f(list==null)
    {
      /.
      ula(list,7="...")
      uls(71,list,"file_list_mistake.iyu",-1,-2)
      ./
      us(71,"visibility","gone")
    }
    else
    {
      us(71,"visibility",true)
      uls(71,list,"list_file.iyu",-1,-2)
    }
  }
}
end fn

fn file_glq_only(path)
t()
{
  fl(path,lj)
  for(c;lj)
  {
    ss(path+"/"+c,ljs)
    fi(ljs,ilj)
    f(ilj==true)
    {
      s tp="@folder-2-1.png"
    }
    f(ilj==false)
    {
      s tp="@document-1.png"
    }
    ula(list,1=null,4=tp,7=c)
    ufnsui()
    {
      
      us(71,"visibility",true)
      uls(71,list,"list_file.iyu",-1,-2)
      
    }
  }
}
end fn

fn file_glq_syj()
t()
{
  ufnsui()
  {
    
    sl(sss.lj,"/",lj)
    sgszl(lj,num)
    syso(num)
    s(num-2,nums)
    s i=""
    s a=1
    f(nums&gt;=3)
    {
      
      for(a;nums)
      {
        
        
        sgsz(lj,a,jg)
        
        
        ss(i+"/"+jg,i)
        s+(1,a)
        
      }
      sss lj=i
      syso(sss.lj)
      fn fun.file_glq_only(sss.lj)
      us(72,"text",sss.lj)
    }
  }
}
end fn

fn srs(lj,a,b)
f(lj==""||a==""||b=="")
{
  tw("请输入完整",0)
}
else
{
  fi(lj,ljs)
  f(ljs==true)
  {
    fl(lj,km)
    for(k;km)
    {
      ss(lj+"/"+k,r)
      fi(r,rs)
      f(rs==true)
      {
        fn fun.srs(r,a,b)
      }
      else
      {
        fr(r,nr)
        sr(nr,a,b,jg)
        fw(r,jg)
      }
    }
    tw("替换完成")
  }
  else
  {
    tw("路径不合法")
  }
}
end fn

fn deldir(a)
f(a=="")
{
  syso("路径为空")
}
else
{
  fi(a,b)
  f(b==true)
  {
    
    fl(a,c)
    for(d;c)
    {
      
      ss(a+"/"+d,e)
      syso(e)
      
      fi(e,h)
      f(h==true)
      {
        ss(a+"/"+d,gh)
        fn fun.deldir(gh)
      }
      else
      {
        fd(e,m)
      }
    }
    
    fd(a,n)
    syso("删除成功")
    
  }
  else
  {
    syso("路径不合法")
  }
}
end fn

fn file_jl(path)
t()
{
  fl(path,lj)
  sgszl(lj,zs)
  s a=zs
  w(a&gt;0)
  {
    s-(1,a)
    sgsz(lj,a,sjc)
    fr(path+"/"+sjc+"/filename",wjm)
    fr(path+"/"+sjc+"/gcode",gcode)
    ula(list,1=null,5=wjm,8=gcode)
    
  }
  ufnsui()
  {
    
    f(list==null)
    {
      /.
      ula(list,7="...")
      uls(71,list,"file_list_mistake.iyu",-1,-2)
      ./
      us(229,"visibility","gone")
    }
    else
    {
      us(229,"visibility",true)
      uls(229,list,"list_jl.iyu",-1,-2)
    }
  }
}
end fn