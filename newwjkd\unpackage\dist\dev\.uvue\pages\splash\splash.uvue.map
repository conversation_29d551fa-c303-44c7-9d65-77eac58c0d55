{"version": 3, "file": "pages/splash/splash.uvue", "names": [], "sources": ["pages/splash/splash.uvue"], "sourcesContent": ["<template>\n  <view class=\"splash-container\">\n    <view class=\"content\">\n      <image class=\"logo\" src=\"/static/images/logo.png\" mode=\"aspectFit\"></image>\n      <text class=\"app-name\">文件快递服务</text>\n      <text class=\"version\">v{{ version }}</text>\n    </view>\n    \n    <view class=\"loading\">\n      <text class=\"loading-text\">正在启动...</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getNoAdStatus } from '@/utils/storage.js'\n\nexport default {\n  data() {\n    return {\n      version: '2.1'\n    }\n  },\n  \n  onLoad() {\n    this.initApp()\n  },\n  \n  methods: {\n    async initApp() {\n      try {\n        // 检查免广告状态\n        const noAd = getNoAdStatus()\n        \n        // 模拟启动延迟\n        await this.delay(2000)\n        \n        // 根据广告状态决定跳转页面\n        if (noAd) {\n          // 直接跳转到主页\n          uni.reLaunch({\n            url: '/pages/index/index'\n          })\n        } else {\n          // 跳转到加载页（可以在这里显示广告）\n          uni.reLaunch({\n            url: '/pages/index/index'\n          })\n        }\n      } catch (error) {\n        console.error('启动初始化失败:', error)\n        // 出错时直接跳转到主页\n        uni.reLaunch({\n          url: '/pages/index/index'\n        })\n      }\n    },\n    \n    delay(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms))\n    }\n  }\n}\n</script>\n\n<style scoped>\n.splash-container {\n  width: 100%;\n  height: 100vh;\n  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n}\n\n.content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 100px;\n}\n\n.logo {\n  width: 120px;\n  height: 120px;\n  margin-bottom: 30px;\n  border-radius: 20px;\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);\n}\n\n.app-name {\n  font-size: 28px;\n  font-weight: bold;\n  color: #ffffff;\n  margin-bottom: 10px;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.version {\n  font-size: 16px;\n  color: rgba(255, 255, 255, 0.8);\n  font-weight: 300;\n}\n\n.loading {\n  position: absolute;\n  bottom: 80px;\n  display: flex;\n  align-items: center;\n}\n\n.loading-text {\n  font-size: 16px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-left: 10px;\n}\n\n/* 添加一些动画效果 */\n.logo {\n  animation: logoFloat 3s ease-in-out infinite;\n}\n\n@keyframes logoFloat {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n}\n\n.loading-text {\n  animation: textBlink 1.5s ease-in-out infinite;\n}\n\n@keyframes textBlink {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n</style>\n"], "mappings": ";AAeA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,EAAE;QACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC;QACH,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC;QACH;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC;MACH;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvD;EACF;AACF;;;;;;SA7DE,mBAUO,mBAVD,KAAK,EAAC,kBAAkB;IAC5B,mBAIO,mBAJD,KAAK,EAAC,SAAS;MACnB,mBAA2E;QAApE,KAAK,EAAC,MAAM;QAAC,GAAG,EAAC,yBAAyB;QAAC,IAAI,EAAC,WAAW;;MAClE,mBAAoC,mBAA9B,KAAK,EAAC,UAAU,KAAC,QAAM;MAC7B,mBAA2C,mBAArC,KAAK,EAAC,SAAS,KAAC,GAAC,mBAAG,YAAO;;IAGnC,mBAEO,mBAFD,KAAK,EAAC,SAAS;MACnB,mBAAyC,mBAAnC,KAAK,EAAC,cAAc,KAAC,SAAO"}