<View id="1" did="0" type="LinearLayout">
<ppt>width=-1
height=-2
orientation=vertical
gravity=center</ppt>
<event></event>
</View>
<View id="2" did="1" type="CardView">
<ppt>width=-1
height=-2
layout_marginLeft=20dp
layout_marginRight=20dp
layout_marginTop=10dp
layout_marginBottom=10dp
app_CardcornerRadius=5dp
app_CardElevation=3dp</ppt>
<event></event>
</View>
<View id="3" did="2" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=horizontal
gravity=center_vertical
clickable=true
BackgroundRipple=#3b000000
</ppt>
<event><eventItme type="clicki">fn fun.file_glq_syj()
fn fun.file_glq(sss.lj)
us(72,"text",sss.lj)</eventItme></event>
</View>
<View id="4" did="3" type="ImageView">
<ppt>width=25dp
height=25dp
src=@document-1.png
layout_marginLeft=15dp
layout_marginTop=15dp
layout_marginBottom=15dp
visibility=gone</ppt>
<event></event>
</View>
<View id="6" did="3" type="LinearLayout">
<ppt>width=-1
height=-1
orientation=vertical
gravity=center
</ppt>
<event></event>
</View>
<View id="7" did="6" type="TextView">
<ppt>width=-2
height=-2
text=文件/夹
textColor=#ff1f999b
layout_marginTop=15dp
layout_marginBottom=15dp</ppt>
<event></event>
</View>
<UIEventset></UIEventset>