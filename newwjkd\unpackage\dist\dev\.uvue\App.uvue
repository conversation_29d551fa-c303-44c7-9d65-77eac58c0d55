
	let firstBackTime = 0
	const __sfc__ = defineApp({
		onLaunch: function () {
			console.log('App Launch', " at App.uvue:5")
			// 检查应用版本
			this.checkAppVersion()
		},
		onShow: function () {
			console.log('App Show', " at App.uvue:10")
		},
		onHide: function () {
			console.log('App Hide', " at App.uvue:13")
		},

		onLastPageBackPress: function () {
			console.log('App LastPageBackPress', " at App.uvue:17")
			if (firstBackTime == 0) {
				uni.showToast({
					title: '再按一次退出应用',
					position: 'bottom',
				})
				firstBackTime = Date.now()
				setTimeout(() => {
					firstBackTime = 0
				}, 2000)
			} else if (Date.now() - firstBackTime < 2000) {
				firstBackTime = Date.now()
				uni.exit()
			}
		},

		onExit: function () {
			console.log('App Exit', " at App.uvue:34")
		},
		methods: {
			// 检查应用版本
			checkAppVersion() {
				const currentVersion = '2.1.0'
				const savedVersion = uni.getStorageSync('app_version')

				if (savedVersion !== currentVersion) {
					// 版本更新，清理缓存或执行升级逻辑
					console.log('应用版本更新:', savedVersion, '->', currentVersion, " at App.uvue:44")
					uni.setStorageSync('app_version', currentVersion)
				}
			}
		}
	})

export default __sfc__
const GenAppStyles = [utsMapOf([["text-primary", padStyleMapOf(utsMapOf([["!color", "var(--color-primary)"]]))], ["text-secondary", padStyleMapOf(utsMapOf([["!color", "var(--color-text-secondary)"]]))], ["text-disabled", padStyleMapOf(utsMapOf([["!color", "var(--color-text-disabled)"]]))], ["text-white", padStyleMapOf(utsMapOf([["!color", "var(--color-white)"]]))], ["text-success", padStyleMapOf(utsMapOf([["!color", "var(--color-success)"]]))], ["text-warning", padStyleMapOf(utsMapOf([["!color", "var(--color-warning)"]]))], ["text-error", padStyleMapOf(utsMapOf([["!color", "var(--color-error)"]]))], ["bg-primary", padStyleMapOf(utsMapOf([["!backgroundColor", "var(--color-primary)"]]))], ["bg-white", padStyleMapOf(utsMapOf([["!backgroundColor", "var(--color-white)"]]))], ["bg-gray-50", padStyleMapOf(utsMapOf([["!backgroundColor", "var(--color-gray-50)"]]))], ["bg-gray-100", padStyleMapOf(utsMapOf([["!backgroundColor", "var(--color-gray-100)"]]))], ["font-light", padStyleMapOf(utsMapOf([["!fontWeight", "var(--font-weight-light)"]]))], ["font-normal", padStyleMapOf(utsMapOf([["!fontWeight", "var(--font-weight-normal)"]]))], ["font-medium", padStyleMapOf(utsMapOf([["!fontWeight", "var(--font-weight-medium)"]]))], ["font-semibold", padStyleMapOf(utsMapOf([["!fontWeight", "var(--font-weight-semibold)"]]))], ["font-bold", padStyleMapOf(utsMapOf([["!fontWeight", "var(--font-weight-bold)"]]))], ["text-xs", padStyleMapOf(utsMapOf([["!fontSize", "var(--font-size-xs)"]]))], ["text-sm", padStyleMapOf(utsMapOf([["!fontSize", "var(--font-size-sm)"]]))], ["text-base", padStyleMapOf(utsMapOf([["!fontSize", "var(--font-size-base)"]]))], ["text-lg", padStyleMapOf(utsMapOf([["!fontSize", "var(--font-size-lg)"]]))], ["text-xl", padStyleMapOf(utsMapOf([["!fontSize", "var(--font-size-xl)"]]))], ["text-2xl", padStyleMapOf(utsMapOf([["!fontSize", "var(--font-size-2xl)"]]))], ["text-3xl", padStyleMapOf(utsMapOf([["!fontSize", "var(--font-size-3xl)"]]))], ["text-center", padStyleMapOf(utsMapOf([["!textAlign", "center"]]))], ["text-left", padStyleMapOf(utsMapOf([["!textAlign", "left"]]))], ["text-right", padStyleMapOf(utsMapOf([["!textAlign", "right"]]))], ["rounded-sm", padStyleMapOf(utsMapOf([["!borderRadius", "var(--border-radius-small)"]]))], ["rounded", padStyleMapOf(utsMapOf([["!borderRadius", "var(--border-radius-medium)"]]))], ["rounded-lg", padStyleMapOf(utsMapOf([["!borderRadius", "var(--border-radius-large)"]]))], ["rounded-xl", padStyleMapOf(utsMapOf([["!borderRadius", "var(--border-radius-xlarge)"]]))], ["shadow-sm", padStyleMapOf(utsMapOf([["!boxShadow", "var(--shadow-small)"]]))], ["shadow", padStyleMapOf(utsMapOf([["!boxShadow", "var(--shadow-medium)"]]))], ["shadow-lg", padStyleMapOf(utsMapOf([["!boxShadow", "var(--shadow-large)"]]))], ["shadow-xl", padStyleMapOf(utsMapOf([["!boxShadow", "var(--shadow-xlarge)"]]))], ["p-1", padStyleMapOf(utsMapOf([["!paddingTop", "var(--spacing-1)"], ["!paddingRight", "var(--spacing-1)"], ["!paddingBottom", "var(--spacing-1)"], ["!paddingLeft", "var(--spacing-1)"]]))], ["p-2", padStyleMapOf(utsMapOf([["!paddingTop", "var(--spacing-2)"], ["!paddingRight", "var(--spacing-2)"], ["!paddingBottom", "var(--spacing-2)"], ["!paddingLeft", "var(--spacing-2)"]]))], ["p-3", padStyleMapOf(utsMapOf([["!paddingTop", "var(--spacing-3)"], ["!paddingRight", "var(--spacing-3)"], ["!paddingBottom", "var(--spacing-3)"], ["!paddingLeft", "var(--spacing-3)"]]))], ["p-4", padStyleMapOf(utsMapOf([["!paddingTop", "var(--spacing-4)"], ["!paddingRight", "var(--spacing-4)"], ["!paddingBottom", "var(--spacing-4)"], ["!paddingLeft", "var(--spacing-4)"]]))], ["p-5", padStyleMapOf(utsMapOf([["!paddingTop", "var(--spacing-5)"], ["!paddingRight", "var(--spacing-5)"], ["!paddingBottom", "var(--spacing-5)"], ["!paddingLeft", "var(--spacing-5)"]]))], ["p-6", padStyleMapOf(utsMapOf([["!paddingTop", "var(--spacing-6)"], ["!paddingRight", "var(--spacing-6)"], ["!paddingBottom", "var(--spacing-6)"], ["!paddingLeft", "var(--spacing-6)"]]))], ["m-1", padStyleMapOf(utsMapOf([["!marginTop", "var(--spacing-1)"], ["!marginRight", "var(--spacing-1)"], ["!marginBottom", "var(--spacing-1)"], ["!marginLeft", "var(--spacing-1)"]]))], ["m-2", padStyleMapOf(utsMapOf([["!marginTop", "var(--spacing-2)"], ["!marginRight", "var(--spacing-2)"], ["!marginBottom", "var(--spacing-2)"], ["!marginLeft", "var(--spacing-2)"]]))], ["m-3", padStyleMapOf(utsMapOf([["!marginTop", "var(--spacing-3)"], ["!marginRight", "var(--spacing-3)"], ["!marginBottom", "var(--spacing-3)"], ["!marginLeft", "var(--spacing-3)"]]))], ["m-4", padStyleMapOf(utsMapOf([["!marginTop", "var(--spacing-4)"], ["!marginRight", "var(--spacing-4)"], ["!marginBottom", "var(--spacing-4)"], ["!marginLeft", "var(--spacing-4)"]]))], ["m-5", padStyleMapOf(utsMapOf([["!marginTop", "var(--spacing-5)"], ["!marginRight", "var(--spacing-5)"], ["!marginBottom", "var(--spacing-5)"], ["!marginLeft", "var(--spacing-5)"]]))], ["m-6", padStyleMapOf(utsMapOf([["!marginTop", "var(--spacing-6)"], ["!marginRight", "var(--spacing-6)"], ["!marginBottom", "var(--spacing-6)"], ["!marginLeft", "var(--spacing-6)"]]))], ["flex", padStyleMapOf(utsMapOf([["!display", "flex"]]))], ["flex-col", padStyleMapOf(utsMapOf([["!flexDirection", "column"]]))], ["flex-row", padStyleMapOf(utsMapOf([["!flexDirection", "row"]]))], ["items-center", padStyleMapOf(utsMapOf([["!alignItems", "center"]]))], ["items-start", padStyleMapOf(utsMapOf([["!alignItems", "flex-start"]]))], ["items-end", padStyleMapOf(utsMapOf([["!alignItems", "flex-end"]]))], ["justify-center", padStyleMapOf(utsMapOf([["!justifyContent", "center"]]))], ["justify-between", padStyleMapOf(utsMapOf([["!justifyContent", "space-between"]]))], ["justify-around", padStyleMapOf(utsMapOf([["!justifyContent", "space-around"]]))], ["flex-1", padStyleMapOf(utsMapOf([["!flex", 1]]))], ["w-full", padStyleMapOf(utsMapOf([["!width", "100%"]]))], ["h-full", padStyleMapOf(utsMapOf([["!height", "100%"]]))], ["overflow-hidden", padStyleMapOf(utsMapOf([["!overflow", "hidden"]]))], ["opacity-50", padStyleMapOf(utsMapOf([["!opacity", 0.5]]))], ["opacity-75", padStyleMapOf(utsMapOf([["!opacity", 0.75]]))], ["btn", padStyleMapOf(utsMapOf([["alignItems", "center"], ["justifyContent", "center"], ["paddingTop", "var(--spacing-3)"], ["paddingRight", "var(--spacing-4)"], ["paddingBottom", "var(--spacing-3)"], ["paddingLeft", "var(--spacing-4)"], ["borderWidth", "medium"], ["borderStyle", "none"], ["borderColor", "#000000"], ["borderRadius", "var(--border-radius-medium)"], ["fontSize", "var(--font-size-base)"], ["fontWeight", "var(--font-weight-medium)"], ["cursor", "pointer"], ["textDecoration", "none"], ["transform:active", "scale(0.98)"], ["opacity:disabled", 0.5], ["cursor:disabled", "not-allowed"]]))], ["btn-primary", padStyleMapOf(utsMapOf([["backgroundColor", "var(--gradient-primary)"], ["color", "var(--color-white)"]]))], ["btn-secondary", padStyleMapOf(utsMapOf([["backgroundColor", "var(--color-white)"], ["color", "var(--color-primary)"], ["borderWidth", 1], ["borderStyle", "solid"], ["borderColor", "var(--color-primary)"]]))], ["btn-outline", padStyleMapOf(utsMapOf([["backgroundColor", "rgba(0,0,0,0)"], ["color", "var(--color-primary)"], ["borderWidth", 1], ["borderStyle", "solid"], ["borderColor", "var(--color-primary)"]]))], ["card", padStyleMapOf(utsMapOf([["backgroundColor", "var(--color-white)"], ["borderRadius", "var(--border-radius-large)"], ["boxShadow", "var(--shadow-medium)"], ["paddingTop", "var(--spacing-4)"], ["paddingRight", "var(--spacing-4)"], ["paddingBottom", "var(--spacing-4)"], ["paddingLeft", "var(--spacing-4)"]]))], ["input", padStyleMapOf(utsMapOf([["width", "100%"], ["paddingTop", "var(--spacing-3)"], ["paddingRight", "var(--spacing-3)"], ["paddingBottom", "var(--spacing-3)"], ["paddingLeft", "var(--spacing-3)"], ["borderWidth", 1], ["borderStyle", "solid"], ["borderColor", "var(--color-border-default)"], ["borderRadius", "var(--border-radius-medium)"], ["fontSize", "var(--font-size-base)"], ["backgroundColor", "var(--color-white)"], ["outline:focus", "none"], ["borderColor:focus", "var(--color-primary)"], ["color::placeholder", "var(--color-text-hint)"]]))], ["uni-row", padStyleMapOf(utsMapOf([["flexDirection", "row"]]))], ["uni-column", padStyleMapOf(utsMapOf([["flexDirection", "column"]]))], ["fade-enter-active", padStyleMapOf(utsMapOf([["transitionProperty", "opacity"], ["transitionDuration", "300ms"], ["transitionTimingFunction", "ease"]]))], ["fade-leave-active", padStyleMapOf(utsMapOf([["transitionProperty", "opacity"], ["transitionDuration", "300ms"], ["transitionTimingFunction", "ease"]]))], ["fade-enter", padStyleMapOf(utsMapOf([["opacity", 0]]))], ["fade-leave-to", padStyleMapOf(utsMapOf([["opacity", 0]]))], ["slide-up-enter-active", padStyleMapOf(utsMapOf([["transitionProperty", "transform"], ["transitionDuration", "300ms"], ["transitionTimingFunction", "ease"]]))], ["slide-up-leave-active", padStyleMapOf(utsMapOf([["transitionProperty", "transform"], ["transitionDuration", "300ms"], ["transitionTimingFunction", "ease"]]))], ["slide-up-enter", padStyleMapOf(utsMapOf([["transform", "translateY(100%)"]]))], ["slide-up-leave-to", padStyleMapOf(utsMapOf([["transform", "translateY(100%)"]]))], ["scale-enter-active", padStyleMapOf(utsMapOf([["transitionProperty", "transform"], ["transitionDuration", "300ms"], ["transitionTimingFunction", "ease"]]))], ["scale-leave-active", padStyleMapOf(utsMapOf([["transitionProperty", "transform"], ["transitionDuration", "300ms"], ["transitionTimingFunction", "ease"]]))], ["scale-enter", padStyleMapOf(utsMapOf([["transform", "scale(0.8)"]]))], ["scale-leave-to", padStyleMapOf(utsMapOf([["transform", "scale(0.8)"]]))], ["@TRANSITION", utsMapOf([["fade-enter-active", utsMapOf([["property", "opacity"], ["duration", "300ms"], ["timingFunction", "ease"]])], ["fade-leave-active", utsMapOf([["property", "opacity"], ["duration", "300ms"], ["timingFunction", "ease"]])], ["slide-up-enter-active", utsMapOf([["property", "transform"], ["duration", "300ms"], ["timingFunction", "ease"]])], ["slide-up-leave-active", utsMapOf([["property", "transform"], ["duration", "300ms"], ["timingFunction", "ease"]])], ["scale-enter-active", utsMapOf([["property", "transform"], ["duration", "300ms"], ["timingFunction", "ease"]])], ["scale-leave-active", utsMapOf([["property", "transform"], ["duration", "300ms"], ["timingFunction", "ease"]])]])]])]

//# sourceMappingURL=App.uvue.map