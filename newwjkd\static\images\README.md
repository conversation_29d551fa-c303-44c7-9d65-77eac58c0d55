# 静态图片资源说明

本目录包含应用所需的图片资源。以下是需要的图片文件：

## 应用图标和Logo
- `logo.png` - 应用Logo (建议尺寸: 512x512px)

## 功能图标
- `pickup-icon.png` - 取件功能图标
- `send-icon.png` - 发件功能图标
- `file-icon.png` - 通用文件图标
- `folder-icon.png` - 文件夹图标

## 文件类型图标
- `image-icon.png` - 图片文件图标
- `video-icon.png` - 视频文件图标
- `audio-icon.png` - 音频文件图标
- `pdf-icon.png` - PDF文件图标
- `doc-icon.png` - Word文档图标

## 支付相关
- `alipay-icon.png` - 支付宝图标
- `wechat-icon.png` - 微信图标
- `alipay-qr.png` - 支付宝收款二维码
- `wechat-qr.png` - 微信收款二维码

## 状态图标
- `empty-record.png` - 空记录状态图标

## 图片规格建议

### 图标类 (40x40px)
- pickup-icon.png
- send-icon.png
- file-icon.png
- folder-icon.png
- image-icon.png
- video-icon.png
- audio-icon.png
- pdf-icon.png
- doc-icon.png
- alipay-icon.png
- wechat-icon.png

### Logo类 (80x80px 或更大)
- logo.png

### 二维码类 (200x200px)
- alipay-qr.png
- wechat-qr.png

### 状态图标 (120x120px)
- empty-record.png

## 设计要求

1. **风格统一**：所有图标应保持统一的设计风格
2. **主题色彩**：建议使用应用主题色 #1f999b
3. **背景透明**：图标类图片建议使用透明背景
4. **高清适配**：提供2x、3x倍图以适配不同分辨率设备

## 临时解决方案

在开发阶段，可以：
1. 使用纯色占位符
2. 使用在线图标库
3. 使用系统默认图标
4. 使用文字代替图标

## 获取图标资源

推荐的图标资源网站：
- [Iconfont](https://www.iconfont.cn/)
- [Feather Icons](https://feathericons.com/)
- [Material Icons](https://material.io/icons/)
- [Heroicons](https://heroicons.com/)

## 注意事项

1. 确保图片格式为PNG或JPG
2. 控制文件大小，避免影响应用性能
3. 遵守相关版权规定
4. 测试在不同设备上的显示效果
