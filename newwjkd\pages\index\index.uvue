<template>
  <view class="container">
    <!-- 顶部Logo和标题 -->
    <view class="header">
      <image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
      <text class="app-title">文件快递服务</text>
    </view>

    <!-- 主要功能区域 -->
    <view class="main-content">
      <!-- 取件功能 -->
      <view class="function-card" @click="showPickupDialog">
        <view class="card-content">
          <text class="card-title">取件</text>
        </view>
      </view>

      <!-- 发件功能 -->
      <view class="function-card send-card" @click="showSendDialog">
        <view class="card-content">
          <text class="card-title send-title">发件</text>
        </view>
      </view>
    </view>

    <!-- 底部功能按钮 -->
    <view class="bottom-actions">
      <view class="action-item" @click="showDonate">
        <text class="action-text">捐赠App</text>
        <view class="action-line"></view>
      </view>
      <view class="action-item" @click="showUploadRecord">
        <text class="action-text">上传记录</text>
        <view class="action-line"></view>
      </view>
    </view>

    <!-- 取件对话框 -->
    <view class="modal-overlay" v-if="showPickup" @click="hidePickupDialog">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <image class="modal-icon" src="/static/images/pickup-icon.png" mode="aspectFit"></image>
        </view>
        <view class="modal-body">
          <view class="input-container">
            <input
              class="pickup-input"
              v-model="pickupCode"
              placeholder="请输入8位取件码"
              maxlength="8"
              type="number"
            />
          </view>
          <view class="modal-actions">
            <button class="modal-btn get-btn" @click="getFile" :disabled="!isValidPickupCode">
              获取
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 发件对话框 -->
    <view class="modal-overlay" v-if="showSend" @click="hideSendDialog">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <image class="modal-icon" src="/static/images/send-icon.png" mode="aspectFit"></image>
        </view>
        <view class="modal-body">
          <text class="modal-title">选择文件</text>
          <view class="file-options">
            <button class="file-option-btn" @click="openCustomFileManager">
              自制文件管理器
            </button>
            <button class="file-option-btn primary" @click="openSystemFileManager">
              系统文件管理器
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 下载进度对话框 -->
    <view class="modal-overlay" v-if="showDownloadProgress" @click.stop>
      <view class="modal-content">
        <view class="progress-header">
          <text class="progress-title">正在下载</text>
        </view>
        <view class="progress-body">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: downloadProgress + '%' }"></view>
          </view>
          <text class="progress-text">{{ downloadProgress }}%</text>
        </view>
      </view>
    </view>

    <!-- 上传结果对话框 -->
    <view class="modal-overlay" v-if="showUploadResult" @click="hideUploadResult">
      <view class="modal-content upload-result" @click.stop>
        <view class="result-header">
          <text class="result-title">{{ uploadResultData.msg }}</text>
        </view>
        <view class="result-body">
          <text class="result-label">文件地址：</text>
          <text class="result-url" @click="copyUrl">{{ uploadResultData.url }}</text>

          <text class="result-label">取件码：</text>
          <text class="result-code" @click="copyCode">{{ uploadResultData.gcode }}</text>

          <text class="result-label">文件二维码：</text>
          <image class="qr-code" :src="uploadResultData.qrCode" mode="aspectFit"></image>
        </view>
        <view class="result-actions">
          <button class="result-btn" @click="saveQRCode">保存二维码</button>
          <button class="result-btn" @click="copyCode">复制取件码</button>
          <button class="result-btn primary" @click="hideUploadResult">确定</button>
        </view>
      </view>
    </view>

    <!-- 加载中对话框 -->
    <view class="modal-overlay" v-if="showLoading" @click.stop>
      <view class="loading-modal">
        <view class="loading-spinner"></view>
        <text class="loading-text">{{ loadingText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getFile, uploadFile, downloadFile } from '@/utils/api.js'
import { saveUploadRecord, getNoAdStatus } from '@/utils/storage.js'
import { showToast, showLoading, hideLoading, showConfirm, validatePickupCode, copyToClipboard } from '@/utils/common.js'

export default {
  data() {
    return {
      // 对话框显示状态
      showPickup: false,
      showSend: false,
      showDownloadProgress: false,
      showUploadResult: false,
      showLoading: false,

      // 取件相关
      pickupCode: '',

      // 下载进度
      downloadProgress: 0,

      // 上传结果
      uploadResultData: {
        msg: '',
        url: '',
        gcode: '',
        qrCode: ''
      },

      // 加载文本
      loadingText: '请稍后...'
    }
  },

  computed: {
    isValidPickupCode() {
      return validatePickupCode(this.pickupCode)
    }
  },

  onLoad() {
    this.checkForUpdates()
    this.checkDonateStatus()
  },

  methods: {
    // 显示取件对话框
    showPickupDialog() {
      this.showPickup = true
      this.pickupCode = ''
    },

    // 隐藏取件对话框
    hidePickupDialog() {
      this.showPickup = false
      this.pickupCode = ''
    },

    // 显示发件对话框
    showSendDialog() {
      // 检查免广告状态
      const noAd = getNoAdStatus()
      if (!noAd) {
        // 这里可以显示广告
        console.log('显示广告')
      }
      this.showSend = true
    },

    // 隐藏发件对话框
    hideSendDialog() {
      this.showSend = false
    },

    // 获取文件
    async getFile() {
      if (!this.isValidPickupCode) {
        showToast('请输入完整的8位取件码')
        return
      }

      this.showLoading = true
      this.loadingText = '请稍后'

      try {
        const result = await getFile(this.pickupCode)

        if (result.status === 1) {
          // 获取成功，开始下载
          const downloadUrl = `https://file.8845.top${result.durl}`
          const fileName = result.wjm

          this.hidePickupDialog()
          this.showLoading = false
          this.showDownloadProgress = true
          this.downloadProgress = 0

          await this.downloadFileWithProgress(downloadUrl, fileName)
        } else {
          showToast(result.msg || '取件失败')
        }
      } catch (error) {
        console.error('获取文件失败:', error)
        showToast('连接超时')
      } finally {
        this.showLoading = false
      }
    },

    // 下载文件并显示进度
    async downloadFileWithProgress(url, fileName) {
      try {
        // 模拟下载进度
        const progressInterval = setInterval(() => {
          if (this.downloadProgress < 90) {
            this.downloadProgress += Math.random() * 10
          } else if (this.downloadProgress < 100) {
            this.downloadProgress += 1
          } else {
            clearInterval(progressInterval)
          }
        }, 200)

        // 实际下载文件
        const filePath = await downloadFile(url, fileName)

        clearInterval(progressInterval)
        this.downloadProgress = 100

        setTimeout(() => {
          this.showDownloadProgress = false
          showToast('下载成功 已保存至 根目录/下载/文件快递/')
        }, 500)

      } catch (error) {
        console.error('下载失败:', error)
        this.showDownloadProgress = false
        showToast('下载失败')
      }
    },

    // 打开自制文件管理器
    openCustomFileManager() {
      this.hideSendDialog()
      uni.navigateTo({
        url: '/pages/fileManager/fileManager'
      })
    },

    // 打开系统文件管理器
    openSystemFileManager() {
      this.hideSendDialog()

      uni.chooseFile({
        count: 1,
        success: (res) => {
          if (res.tempFiles && res.tempFiles.length > 0) {
            const file = res.tempFiles[0]
            this.uploadSelectedFile(file.path, file.name)
          }
        },
        fail: (error) => {
          console.error('选择文件失败:', error)
          showToast('选择文件失败')
        }
      })
    },

    // 上传选中的文件
    async uploadSelectedFile(filePath, fileName) {
      const confirmed = await showConfirm(`是否上传文件：${fileName}？`)
      if (!confirmed) return

      this.showLoading = true
      this.loadingText = '上传中'

      try {
        const result = await uploadFile(filePath)

        if (result.status === 1) {
          // 上传成功
          const gcode = result.get_code
          const fileUrl = `https://file.8845.top/file/${gcode}/${fileName}`

          // 生成二维码（这里使用简单的二维码API）
          const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(fileUrl)}`

          this.uploadResultData = {
            msg: result.msg,
            url: fileUrl,
            gcode: gcode,
            qrCode: qrCodeUrl
          }

          // 保存上传记录
          saveUploadRecord({
            filename: fileName,
            gcode: gcode,
            url: fileUrl
          })

          this.showLoading = false
          this.showUploadResult = true

        } else {
          showToast(result.msg || '上传失败')
        }
      } catch (error) {
        console.error('上传失败:', error)
        showToast('连接超时')
      } finally {
        this.showLoading = false
      }
    },

    // 隐藏上传结果对话框
    hideUploadResult() {
      this.showUploadResult = false
    },

    // 复制URL
    async copyUrl() {
      await copyToClipboard(this.uploadResultData.url)
    },

    // 复制取件码
    async copyCode() {
      await copyToClipboard(this.uploadResultData.gcode)
    },

    // 保存二维码
    saveQRCode() {
      // 这里实现保存二维码的逻辑
      showToast('已保存至 根目录/下载/文件快递/二维码/')
    },

    // 显示捐赠页面
    showDonate() {
      uni.navigateTo({
        url: '/pages/donate/donate'
      })
    },

    // 显示上传记录
    showUploadRecord() {
      uni.navigateTo({
        url: '/pages/uploadRecord/uploadRecord'
      })
    },

    // 检查更新
    async checkForUpdates() {
      // 这里实现检查更新的逻辑
      console.log('检查更新')
    },

    // 检查捐赠状态
    async checkDonateStatus() {
      // 这里实现检查捐赠状态的逻辑
      console.log('检查捐赠状态')
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px 40px;
  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);
}

.logo {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  border-radius: 15px;
}

.app-title {
  font-size: 20px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 功能卡片 */
.function-card {
  background-color: #ffffff;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;
}

.function-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.send-card {
  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);
  border: none;
}

.card-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #1f999b;
}

.send-title {
  color: #ffffff;
}

/* 底部操作区域 */
.bottom-actions {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  margin-top: auto;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.action-text {
  font-size: 13px;
  font-weight: bold;
  color: #b8b8b8;
  margin-bottom: 5px;
}

.action-line {
  width: 50px;
  height: 2px;
  background-color: #b8b8b8;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(59, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #ffffff;
  border-radius: 15px;
  margin: 20px;
  max-width: 90%;
  width: 320px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: center;
  padding: 20px 20px 10px;
}

.modal-icon {
  width: 80px;
  height: 80px;
}

.modal-body {
  padding: 0 20px 20px;
}

.modal-title {
  font-size: 17px;
  font-weight: bold;
  color: #1f999b;
  text-align: center;
  margin-bottom: 20px;
}

/* 输入框样式 */
.input-container {
  margin-bottom: 20px;
}

.pickup-input {
  width: 100%;
  height: 45px;
  border: none;
  background-color: #f8f8f8;
  border-radius: 10px;
  padding: 0 15px;
  font-size: 15px;
  text-align: center;
  color: #1f999b;
}

.pickup-input::placeholder {
  color: #bcbcbc;
}

/* 按钮样式 */
.modal-actions {
  display: flex;
  justify-content: center;
}

.modal-btn {
  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);
  color: #ffffff;
  border: none;
  border-radius: 10px;
  padding: 12px 30px;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
}

.modal-btn:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

.file-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.file-option-btn {
  background-color: #ffffff;
  border: 2px solid #1f999b;
  color: #1f999b;
  border-radius: 10px;
  padding: 12px 20px;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
}

.file-option-btn.primary {
  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);
  color: #ffffff;
  border: none;
}

/* 进度条样式 */
.progress-header {
  padding: 20px;
  text-align: center;
}

.progress-title {
  font-size: 17px;
  font-weight: bold;
  color: #1f999b;
}

.progress-body {
  padding: 0 20px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1f999b 0%, #16a085 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #999999;
  font-weight: 500;
}

/* 上传结果样式 */
.upload-result {
  max-height: 80vh;
  overflow-y: auto;
}

.result-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.result-title {
  font-size: 17px;
  font-weight: bold;
  color: #1f999b;
}

.result-body {
  padding: 20px;
}

.result-label {
  font-size: 14px;
  color: #1f999b;
  font-weight: bold;
  display: block;
  margin-top: 15px;
  margin-bottom: 5px;
}

.result-label:first-child {
  margin-top: 0;
}

.result-url, .result-code {
  font-size: 14px;
  color: #f98b8b;
  font-weight: bold;
  word-break: break-all;
  cursor: pointer;
  padding: 8px;
  background-color: #f8f8f8;
  border-radius: 5px;
}

.qr-code {
  width: 120px;
  height: 120px;
  margin: 10px 0;
  border-radius: 5px;
}

.result-actions {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.result-btn {
  background-color: #ffffff;
  border: 1px solid #1f999b;
  color: #1f999b;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  flex: 1;
  margin: 0 5px;
}

.result-btn.primary {
  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);
  color: #ffffff;
  border: none;
}

/* 加载中样式 */
.loading-modal {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 150px;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1f999b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 15px;
  color: #1f999b;
  font-weight: bold;
}

/* 主页面样式 */
.container {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px 40px;
  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);
}

.logo {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  border-radius: 15px;
}

.app-title {
  font-size: 20px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 功能卡片 */
.function-card {
  background-color: #ffffff;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;
}

.function-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.send-card {
  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);
  border: none;
}

.card-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #1f999b;
}

.send-title {
  color: #ffffff;
}

/* 底部操作区域 */
.bottom-actions {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  margin-top: auto;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.action-text {
  font-size: 13px;
  font-weight: bold;
  color: #b8b8b8;
  margin-bottom: 5px;
}

.action-line {
  width: 50px;
  height: 2px;
  background-color: #b8b8b8;
}
</style>
