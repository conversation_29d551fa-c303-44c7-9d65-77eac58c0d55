import android.content.ContentUris;
import android.content.Intent;
import android.net.Uri;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.database.Cursor;
import android.content.Context;
import android.os.Environment;

public static String getRealPath(Context context, Uri uri)
{
  String imagePath = null;
  if(DocumentsContract.isDocumentUri(context, uri))
  {
    // 如果是document类型的Uri，则通过document id处理
            
    String docId = DocumentsContract.getDocumentId(uri);
        
    if ("com.android.providers.media.documents".equals(uri.getAuthority()))
            
    {
                
      String id = docId.split(":")[1];           
      String selection = MediaStore.Images.Media._ID + "=" + id;
  
      imagePath = getImagePath(context, MediaStore.Images.Media.EXTERNAL_CONTENT_URI, selection);
    }
            
    else if ("com.android.providers.downloads.documents".equals(uri.getAuthority()))
            
    {
                
      Uri contentUri = ContentUris.withAppendedId(Uri.parse("content://downloads/public_downloads"), Long.valueOf(docId));
      imagePath = getImagePath(context, contentUri, null);
    }
    else if ("com.android.externalstorage.documents".equals(uri.getAuthority()))
    {
      String[] split = docId.split(":");
	  String type = split[0];
      if ("primary".equalsIgnoreCase(type)) 
      {
        imagePath = Environment.getExternalStorageDirectory() + "/" + split[1];
      }
    }
  }
  else if("content".equalsIgnoreCase(uri.getScheme()))
  {
    // content类型普通方式处理
    imagePath = getImagePath(context, uri, null);
  }
  else if("file".equalsIgnoreCase(uri.getScheme()))
  {
    // file类型直接获取图片路径
    imagePath = uri.getPath();
  }
  return imagePath;
}

public String getImagePath(Context context, Uri uri, String selection)
{
  String path = null;
  // 通过Uri和selection来获取真实的图片路径
  Cursor cursor = context.getContentResolver().query(uri, null, selection, null, null);
  if (cursor != null)
  {
    syso(cursor.moveToFirst());
    if (cursor.moveToFirst())
    {
      path = cursor.getString(cursor.getColumnIndex(MediaStore.Images.Media.DATA));
    }
    cursor.close();
  }
  return path;
}