/* 全局样式文件 */
/* 对应原项目中的着色.myu功能 */

/* CSS变量定义 */
:root {
  /* 主色调 */
  --color-primary: #1f999b;
  --color-primary-dark: #16a085;
  --color-primary-light: #4db6ac;
  
  /* 辅助色 */
  --color-secondary: #f98b8b;
  --color-accent: #ff6b6b;
  
  /* 状态色 */
  --color-success: #4caf50;
  --color-warning: #ff9800;
  --color-error: #f44336;
  --color-info: #2196f3;
  
  /* 中性色 */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-gray-50: #fafafa;
  --color-gray-100: #f5f5f5;
  --color-gray-200: #eeeeee;
  --color-gray-300: #e0e0e0;
  --color-gray-400: #bdbdbd;
  --color-gray-500: #9e9e9e;
  --color-gray-600: #757575;
  --color-gray-700: #616161;
  --color-gray-800: #424242;
  --color-gray-900: #212121;
  
  /* 文本色 */
  --color-text-primary: #333333;
  --color-text-secondary: #666666;
  --color-text-disabled: #999999;
  --color-text-hint: #cccccc;
  
  /* 背景色 */
  --color-bg-default: #ffffff;
  --color-bg-paper: #f8f8f8;
  --color-bg-disabled: #f0f0f0;
  
  /* 边框色 */
  --color-border-light: #f0f0f0;
  --color-border-default: #e0e0e0;
  --color-border-dark: #cccccc;
  
  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-accent) 100%);
  
  /* 阴影 */
  --shadow-small: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-large: 0 8px 16px rgba(0, 0, 0, 0.1);
  --shadow-xlarge: 0 12px 24px rgba(0, 0, 0, 0.15);
  
  /* 圆角 */
  --border-radius-small: 4px;
  --border-radius-medium: 8px;
  --border-radius-large: 12px;
  --border-radius-xlarge: 16px;
  
  /* 间距 */
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-8: 32px;
  --spacing-10: 40px;
  --spacing-12: 48px;
  --spacing-16: 64px;
  --spacing-20: 80px;
  --spacing-24: 96px;
  
  /* 字体大小 */
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 28px;
  --font-size-5xl: 32px;
  
  /* 字体粗细 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;
  
  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
}

/* 全局重置样式 */
* {
  box-sizing: border-box;
}

page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-default);
}

/* 通用工具类 */
.text-primary { color: var(--color-primary) !important; }
.text-secondary { color: var(--color-text-secondary) !important; }
.text-disabled { color: var(--color-text-disabled) !important; }
.text-white { color: var(--color-white) !important; }
.text-success { color: var(--color-success) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-error { color: var(--color-error) !important; }

.bg-primary { background-color: var(--color-primary) !important; }
.bg-white { background-color: var(--color-white) !important; }
.bg-gray-50 { background-color: var(--color-gray-50) !important; }
.bg-gray-100 { background-color: var(--color-gray-100) !important; }

.font-light { font-weight: var(--font-weight-light) !important; }
.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }

.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }
.text-3xl { font-size: var(--font-size-3xl) !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.rounded-sm { border-radius: var(--border-radius-small) !important; }
.rounded { border-radius: var(--border-radius-medium) !important; }
.rounded-lg { border-radius: var(--border-radius-large) !important; }
.rounded-xl { border-radius: var(--border-radius-xlarge) !important; }
.rounded-full { border-radius: 50% !important; }

.shadow-sm { box-shadow: var(--shadow-small) !important; }
.shadow { box-shadow: var(--shadow-medium) !important; }
.shadow-lg { box-shadow: var(--shadow-large) !important; }
.shadow-xl { box-shadow: var(--shadow-xlarge) !important; }

.p-1 { padding: var(--spacing-1) !important; }
.p-2 { padding: var(--spacing-2) !important; }
.p-3 { padding: var(--spacing-3) !important; }
.p-4 { padding: var(--spacing-4) !important; }
.p-5 { padding: var(--spacing-5) !important; }
.p-6 { padding: var(--spacing-6) !important; }

.m-1 { margin: var(--spacing-1) !important; }
.m-2 { margin: var(--spacing-2) !important; }
.m-3 { margin: var(--spacing-3) !important; }
.m-4 { margin: var(--spacing-4) !important; }
.m-5 { margin: var(--spacing-5) !important; }
.m-6 { margin: var(--spacing-6) !important; }

.flex { display: flex !important; }
.flex-col { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.items-center { align-items: center !important; }
.items-start { align-items: flex-start !important; }
.items-end { align-items: flex-end !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.flex-1 { flex: 1 !important; }

.w-full { width: 100% !important; }
.h-full { height: 100% !important; }
.min-h-screen { min-height: 100vh !important; }

.overflow-hidden { overflow: hidden !important; }
.overflow-scroll { overflow: scroll !important; }

.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }

.transition { transition: all var(--duration-normal) ease !important; }
.transition-fast { transition: all var(--duration-fast) ease !important; }
.transition-slow { transition: all var(--duration-slow) ease !important; }

/* 通用组件样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  border-radius: var(--border-radius-medium);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-normal) ease;
  text-decoration: none;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--color-white);
}

.btn-secondary {
  background-color: var(--color-white);
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn:active {
  transform: scale(0.98);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.card {
  background-color: var(--color-white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-medium);
  padding: var(--spacing-4);
}

.input {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--color-border-default);
  border-radius: var(--border-radius-medium);
  font-size: var(--font-size-base);
  background-color: var(--color-white);
  transition: border-color var(--duration-normal) ease;
}

.input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.input::placeholder {
  color: var(--color-text-hint);
}
