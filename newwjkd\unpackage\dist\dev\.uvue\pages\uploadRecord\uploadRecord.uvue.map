{"version": 3, "file": "pages/uploadRecord/uploadRecord.uvue", "names": [], "sources": ["pages/uploadRecord/uploadRecord.uvue"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 头部 -->\n    <view class=\"header\">\n      <text class=\"header-title\">上传记录</text>\n      <text class=\"header-subtitle\">共 {{ records.length }} 条记录</text>\n    </view>\n    \n    <!-- 记录列表 -->\n    <scroll-view class=\"record-list\" scroll-y=\"true\" v-if=\"records.length > 0\">\n      <view class=\"record-item\" v-for=\"(record, index) in records\" :key=\"record.id\">\n        <view class=\"record-header\">\n          <text class=\"file-name\">{{ record.filename }}</text>\n          <text class=\"upload-time\">{{ record.uploadTime }}</text>\n        </view>\n        <view class=\"record-body\">\n          <view class=\"record-row\">\n            <text class=\"label\">取件码：</text>\n            <text class=\"value code\" @click=\"copyCode(record.gcode)\">{{ record.gcode }}</text>\n          </view>\n          <view class=\"record-row\" v-if=\"record.url\">\n            <text class=\"label\">文件地址：</text>\n            <text class=\"value url\" @click=\"copyUrl(record.url)\">{{ record.url }}</text>\n          </view>\n        </view>\n        <view class=\"record-actions\">\n          <button class=\"action-btn copy\" @click=\"copyCode(record.gcode)\">复制取件码</button>\n          <button class=\"action-btn delete\" @click=\"deleteRecord(record.id, index)\">删除</button>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 空状态 -->\n    <view class=\"empty-state\" v-else>\n      <image class=\"empty-icon\" src=\"/static/images/empty-record.png\" mode=\"aspectFit\"></image>\n      <text class=\"empty-text\">暂无上传记录</text>\n      <text class=\"empty-desc\">上传文件后会在这里显示记录</text>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"bottom-bar\" v-if=\"records.length > 0\">\n      <button class=\"bottom-btn clear\" @click=\"showClearConfirm\">清空记录</button>\n    </view>\n    \n    <!-- 清空确认对话框 -->\n    <view class=\"modal-overlay\" v-if=\"showClearDialog\" @click=\"hideClearDialog\">\n      <view class=\"modal-content\" @click.stop>\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">清空记录</text>\n        </view>\n        <view class=\"modal-body\">\n          <text class=\"modal-text\">确定要清空所有上传记录吗？此操作不可恢复。</text>\n        </view>\n        <view class=\"modal-actions\">\n          <button class=\"modal-btn cancel\" @click=\"hideClearDialog\">取消</button>\n          <button class=\"modal-btn confirm\" @click=\"clearAllRecords\">确定</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getUploadRecords, deleteUploadRecord, clearUploadRecords } from '@/utils/storage.js'\nimport { showToast, showConfirm, copyToClipboard } from '@/utils/common.js'\n\nexport default {\n  data() {\n    return {\n      records: [],\n      showClearDialog: false\n    }\n  },\n  \n  onLoad() {\n    this.loadRecords()\n  },\n  \n  onShow() {\n    // 每次显示页面时重新加载记录\n    this.loadRecords()\n  },\n  \n  methods: {\n    // 加载记录\n    loadRecords() {\n      this.records = getUploadRecords()\n    },\n    \n    // 复制取件码\n    async copyCode(code) {\n      await copyToClipboard(code)\n    },\n    \n    // 复制URL\n    async copyUrl(url) {\n      await copyToClipboard(url)\n    },\n    \n    // 删除记录\n    async deleteRecord(id, index) {\n      const confirmed = await showConfirm('确定要删除这条记录吗？')\n      if (confirmed) {\n        const success = deleteUploadRecord(id)\n        if (success) {\n          this.records.splice(index, 1)\n          showToast('删除成功')\n        } else {\n          showToast('删除失败')\n        }\n      }\n    },\n    \n    // 显示清空确认对话框\n    showClearConfirm() {\n      this.showClearDialog = true\n    },\n    \n    // 隐藏清空确认对话框\n    hideClearDialog() {\n      this.showClearDialog = false\n    },\n    \n    // 清空所有记录\n    clearAllRecords() {\n      const success = clearUploadRecords()\n      if (success) {\n        this.records = []\n        this.showClearDialog = false\n        showToast('清空成功')\n      } else {\n        showToast('清空失败')\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #f8f8f8;\n}\n\n/* 头部 */\n.header {\n  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);\n  padding: 20px;\n  color: #ffffff;\n}\n\n.header-title {\n  font-size: 20px;\n  font-weight: bold;\n  margin-bottom: 5px;\n}\n\n.header-subtitle {\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n/* 记录列表 */\n.record-list {\n  flex: 1;\n  padding: 15px;\n}\n\n.record-item {\n  background-color: #ffffff;\n  border-radius: 12px;\n  margin-bottom: 15px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.record-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 12px;\n  padding-bottom: 8px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.file-name {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333333;\n  flex: 1;\n  margin-right: 10px;\n}\n\n.upload-time {\n  font-size: 12px;\n  color: #999999;\n}\n\n.record-body {\n  margin-bottom: 12px;\n}\n\n.record-row {\n  display: flex;\n  margin-bottom: 8px;\n  align-items: flex-start;\n}\n\n.label {\n  font-size: 14px;\n  color: #666666;\n  width: 80px;\n  flex-shrink: 0;\n}\n\n.value {\n  font-size: 14px;\n  flex: 1;\n  word-break: break-all;\n}\n\n.value.code {\n  color: #f98b8b;\n  font-weight: bold;\n  cursor: pointer;\n}\n\n.value.url {\n  color: #1f999b;\n  cursor: pointer;\n}\n\n.record-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.action-btn {\n  flex: 1;\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 12px;\n  font-weight: bold;\n  border: none;\n  cursor: pointer;\n}\n\n.action-btn.copy {\n  background-color: #1f999b;\n  color: #ffffff;\n}\n\n.action-btn.delete {\n  background-color: #ff6b6b;\n  color: #ffffff;\n}\n\n/* 空状态 */\n.empty-state {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  padding: 40px 20px;\n}\n\n.empty-icon {\n  width: 120px;\n  height: 120px;\n  margin-bottom: 20px;\n  opacity: 0.6;\n}\n\n.empty-text {\n  font-size: 18px;\n  color: #999999;\n  font-weight: bold;\n  margin-bottom: 8px;\n}\n\n.empty-desc {\n  font-size: 14px;\n  color: #cccccc;\n  text-align: center;\n}\n\n/* 底部操作栏 */\n.bottom-bar {\n  padding: 15px 20px;\n  background-color: #ffffff;\n  border-top: 1px solid #e0e0e0;\n}\n\n.bottom-btn {\n  width: 100%;\n  padding: 12px;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: bold;\n  border: none;\n  cursor: pointer;\n}\n\n.bottom-btn.clear {\n  background-color: #ff6b6b;\n  color: #ffffff;\n}\n\n/* 模态框样式 */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background-color: #ffffff;\n  border-radius: 12px;\n  margin: 20px;\n  max-width: 300px;\n  width: 90%;\n  overflow: hidden;\n}\n\n.modal-header {\n  padding: 20px 20px 10px;\n  text-align: center;\n}\n\n.modal-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #333333;\n}\n\n.modal-body {\n  padding: 10px 20px 20px;\n  text-align: center;\n}\n\n.modal-text {\n  font-size: 14px;\n  color: #666666;\n  line-height: 1.5;\n}\n\n.modal-actions {\n  display: flex;\n  border-top: 1px solid #f0f0f0;\n}\n\n.modal-btn {\n  flex: 1;\n  padding: 15px;\n  border: none;\n  background-color: #ffffff;\n  font-size: 16px;\n  font-weight: bold;\n  cursor: pointer;\n}\n\n.modal-btn.cancel {\n  color: #999999;\n  border-right: 1px solid #f0f0f0;\n}\n\n.modal-btn.confirm {\n  color: #ff6b6b;\n}\n\n.modal-btn:active {\n  background-color: #f8f8f8;\n}\n</style>\n"], "mappings": ";AA+DA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE1E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB;MACF;IACF,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC5B,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;IACF;EACF;AACF;;;;;;SAtIE,mBA0DO,mBA1DD,KAAK,EAAC,WAAW;IAErB,mBAGO,mBAHD,KAAK,EAAC,QAAQ;MAClB,mBAAsC,mBAAhC,KAAK,EAAC,cAAc,KAAC,MAAI;MAC/B,mBAA+D,mBAAzD,KAAK,EAAC,iBAAiB,KAAC,IAAE,mBAAG,YAAO,CAAC,MAAM,IAAG,MAAI;;IAIH,YAAO,CAAC,MAAM;QAArE,mBAqBc;;UArBD,KAAK,EAAC,aAAa;UAAC,UAAQ,EAAC,MAAM;;UAC9C,mBAmBO,yCAnB6C,YAAO,GAAzB,MAAM,EAAE,KAAK,EAAb,OAAM;mBAAxC,mBAmBO;cAnBD,KAAK,EAAC,aAAa;cAAqC,GAAG,EAAE,MAAM,CAAC,EAAE;;cAC1E,mBAGO,mBAHD,KAAK,EAAC,eAAe;gBACzB,mBAAoD,mBAA9C,KAAK,EAAC,WAAW,qBAAI,MAAM,CAAC,QAAQ;gBAC1C,mBAAwD,mBAAlD,KAAK,EAAC,aAAa,qBAAI,MAAM,CAAC,UAAU;;cAEhD,mBASO,mBATD,KAAK,EAAC,aAAa;gBACvB,mBAGO,mBAHD,KAAK,EAAC,YAAY;kBACtB,mBAA+B,mBAAzB,KAAK,EAAC,OAAO,KAAC,MAAI;kBACxB,mBAAkF;oBAA5E,KAAK,EAAC,YAAY;oBAAE,OAAK,SAAE,aAAQ,CAAC,MAAM,CAAC,KAAK;sCAAM,MAAM,CAAC,KAAK;;uBAE3C,MAAM,CAAC,GAAG;oBAAzC,mBAGO;;sBAHD,KAAK,EAAC,YAAY;;sBACtB,mBAAgC,mBAA1B,KAAK,EAAC,OAAO,KAAC,OAAK;sBACzB,mBAA4E;wBAAtE,KAAK,EAAC,WAAW;wBAAE,OAAK,SAAE,YAAO,CAAC,MAAM,CAAC,GAAG;0CAAM,MAAM,CAAC,GAAG;;;;cAGtE,mBAGO,mBAHD,KAAK,EAAC,gBAAgB;gBAC1B,mBAA8E;kBAAtE,KAAK,EAAC,iBAAiB;kBAAE,OAAK,SAAE,aAAQ,CAAC,MAAM,CAAC,KAAK;oBAAG,OAAK;gBACrE,mBAAqF;kBAA7E,KAAK,EAAC,mBAAmB;kBAAE,OAAK,SAAE,iBAAY,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK;oBAAG,IAAE;;;;;QAMlF,mBAIO;;UAJD,KAAK,EAAC,aAAa;;UACvB,mBAAyF;YAAlF,KAAK,EAAC,YAAY;YAAC,GAAG,EAAC,iCAAiC;YAAC,IAAI,EAAC,WAAW;;UAChF,mBAAsC,mBAAhC,KAAK,EAAC,YAAY,KAAC,QAAM;UAC/B,mBAA6C,mBAAvC,KAAK,EAAC,YAAY,KAAC,eAAa;;IAIT,YAAO,CAAC,MAAM;QAA7C,mBAEO;;UAFD,KAAK,EAAC,YAAY;;UACtB,mBAAwE;YAAhE,KAAK,EAAC,kBAAkB;YAAE,OAAK,EAAE,qBAAgB;cAAE,MAAI;;;WAI/B,oBAAe;QAAjD,mBAaO;;UAbD,KAAK,EAAC,eAAe;UAAyB,OAAK,EAAE,oBAAe;;UACxE,mBAWO;YAXD,KAAK,EAAC,eAAe;YAAE,OAAK,gBAAN,QAAW;;YACrC,mBAEO,mBAFD,KAAK,EAAC,cAAc;cACxB,mBAAqC,mBAA/B,KAAK,EAAC,aAAa,KAAC,MAAI;;YAEhC,mBAEO,mBAFD,KAAK,EAAC,YAAY;cACtB,mBAAqD,mBAA/C,KAAK,EAAC,YAAY,KAAC,uBAAqB;;YAEhD,mBAGO,mBAHD,KAAK,EAAC,eAAe;cACzB,mBAAqE;gBAA7D,KAAK,EAAC,kBAAkB;gBAAE,OAAK,EAAE,oBAAe;kBAAE,IAAE;cAC5D,mBAAsE;gBAA9D,KAAK,EAAC,mBAAmB;gBAAE,OAAK,EAAE,oBAAe;kBAAE,IAAE"}