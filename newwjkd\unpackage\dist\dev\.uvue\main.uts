import App from './App.uvue'

import { createSSRApp } from 'vue'
export function createApp() {
	const app = createSSRApp(App)
	return {
		app
	}
}
export function main(app: IApp) {
    definePageRoutes();
    defineAppConfig();
    
    (createApp()['app'] as VueApp).mount(app);
}

export class UniAppConfig extends io.dcloud.uniapp.appframe.AppConfig {
    override name: string = "文件快递服务"
    override appid: string = "__UNI__WJKD"
    override versionName: string = "2.1.0"
    override versionCode: string = "210"
    override uniCompilerVersion: string = "4.29"
    
    constructor() { super() }
}

import GenPagesSplashSplashClass from './pages/splash/splash.uvue?type=page'
import GenPagesLoadingLoadingClass from './pages/loading/loading.uvue?type=page'
import GenPagesIndexIndexClass from './pages/index/index.uvue?type=page'
import GenPagesFileManagerFileManagerClass from './pages/fileManager/fileManager.uvue?type=page'
import GenPagesUploadRecordUploadRecordClass from './pages/uploadRecord/uploadRecord.uvue?type=page'
import GenPagesDonateDonateClass from './pages/donate/donate.uvue?type=page'
function definePageRoutes() {
__uniRoutes.push({ path: "pages/splash/splash", component: GenPagesSplashSplashClass, meta: { isQuit: true } as UniPageMeta, style: utsMapOf([["navigationBarTitleText","启动页"],["navigationStyle","custom"]]) } as UniPageRoute)
__uniRoutes.push({ path: "pages/loading/loading", component: GenPagesLoadingLoadingClass, meta: { isQuit: false } as UniPageMeta, style: utsMapOf([["navigationBarTitleText","加载中"],["navigationStyle","custom"]]) } as UniPageRoute)
__uniRoutes.push({ path: "pages/index/index", component: GenPagesIndexIndexClass, meta: { isQuit: false } as UniPageMeta, style: utsMapOf([["navigationBarTitleText","文件快递服务"],["navigationBarBackgroundColor","#1f999b"],["navigationBarTextStyle","white"]]) } as UniPageRoute)
__uniRoutes.push({ path: "pages/fileManager/fileManager", component: GenPagesFileManagerFileManagerClass, meta: { isQuit: false } as UniPageMeta, style: utsMapOf([["navigationBarTitleText","文件管理器"],["navigationBarBackgroundColor","#1f999b"],["navigationBarTextStyle","white"]]) } as UniPageRoute)
__uniRoutes.push({ path: "pages/uploadRecord/uploadRecord", component: GenPagesUploadRecordUploadRecordClass, meta: { isQuit: false } as UniPageMeta, style: utsMapOf([["navigationBarTitleText","上传记录"],["navigationBarBackgroundColor","#1f999b"],["navigationBarTextStyle","white"]]) } as UniPageRoute)
__uniRoutes.push({ path: "pages/donate/donate", component: GenPagesDonateDonateClass, meta: { isQuit: false } as UniPageMeta, style: utsMapOf([["navigationBarTitleText","捐赠支持"],["navigationBarBackgroundColor","#1f999b"],["navigationBarTextStyle","white"]]) } as UniPageRoute)
}
const __uniTabBar: Map<string, any | null> | null = utsMapOf([["color","#b8b8b8"],["selectedColor","#1f999b"],["backgroundColor","#ffffff"],["borderStyle","black"],["list",[utsMapOf([["pagePath","pages/index/index"],["text","首页"],["iconPath","static/icons/home.png"],["selectedIconPath","static/icons/home-active.png"]])]]])
const __uniLaunchPage: Map<string, any | null> = utsMapOf([["url","pages/splash/splash"],["style",utsMapOf([["navigationBarTitleText","启动页"],["navigationStyle","custom"]])]])
function defineAppConfig(){
  __uniConfig.entryPagePath = '/pages/splash/splash'
  __uniConfig.globalStyle = utsMapOf([["navigationBarTextStyle","white"],["navigationBarTitleText","文件快递服务"],["navigationBarBackgroundColor","#1f999b"],["backgroundColor","#ffffff"]])
  __uniConfig.tabBar = __uniTabBar as Map<string, any> | null
  __uniConfig.conditionUrl = ''
  __uniConfig.uniIdRouter = utsMapOf()
  
  __uniConfig.ready = true
}
