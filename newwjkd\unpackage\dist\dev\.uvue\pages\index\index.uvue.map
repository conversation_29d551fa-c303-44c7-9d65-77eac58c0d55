{"version": 3, "file": "pages/index/index.uvue", "names": [], "sources": ["pages/index/index.uvue"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 顶部Logo和标题 -->\r\n    <view class=\"header\">\r\n      <image class=\"logo\" src=\"/static/images/logo.png\" mode=\"aspectFit\"></image>\r\n      <text class=\"app-title\">文件快递服务</text>\r\n    </view>\r\n\r\n    <!-- 主要功能区域 -->\r\n    <view class=\"main-content\">\r\n      <!-- 取件功能 -->\r\n      <view class=\"function-card\" @click=\"showPickupDialog\">\r\n        <view class=\"card-content\">\r\n          <text class=\"card-title\">取件</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 发件功能 -->\r\n      <view class=\"function-card send-card\" @click=\"showSendDialog\">\r\n        <view class=\"card-content\">\r\n          <text class=\"card-title send-title\">发件</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 底部功能按钮 -->\r\n    <view class=\"bottom-actions\">\r\n      <view class=\"action-item\" @click=\"showDonate\">\r\n        <text class=\"action-text\">捐赠App</text>\r\n        <view class=\"action-line\"></view>\r\n      </view>\r\n      <view class=\"action-item\" @click=\"showUploadRecord\">\r\n        <text class=\"action-text\">上传记录</text>\r\n        <view class=\"action-line\"></view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 取件对话框 -->\r\n    <view class=\"modal-overlay\" v-if=\"showPickup\" @click=\"hidePickupDialog\">\r\n      <view class=\"modal-content\" @click.stop>\r\n        <view class=\"modal-header\">\r\n          <image class=\"modal-icon\" src=\"/static/images/pickup-icon.png\" mode=\"aspectFit\"></image>\r\n        </view>\r\n        <view class=\"modal-body\">\r\n          <view class=\"input-container\">\r\n            <input\r\n              class=\"pickup-input\"\r\n              v-model=\"pickupCode\"\r\n              placeholder=\"请输入8位取件码\"\r\n              maxlength=\"8\"\r\n              type=\"number\"\r\n            />\r\n          </view>\r\n          <view class=\"modal-actions\">\r\n            <button class=\"modal-btn get-btn\" @click=\"getFile\" :disabled=\"!isValidPickupCode\">\r\n              获取\r\n            </button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 发件对话框 -->\r\n    <view class=\"modal-overlay\" v-if=\"showSend\" @click=\"hideSendDialog\">\r\n      <view class=\"modal-content\" @click.stop>\r\n        <view class=\"modal-header\">\r\n          <image class=\"modal-icon\" src=\"/static/images/send-icon.png\" mode=\"aspectFit\"></image>\r\n        </view>\r\n        <view class=\"modal-body\">\r\n          <text class=\"modal-title\">选择文件</text>\r\n          <view class=\"file-options\">\r\n            <button class=\"file-option-btn\" @click=\"openCustomFileManager\">\r\n              自制文件管理器\r\n            </button>\r\n            <button class=\"file-option-btn primary\" @click=\"openSystemFileManager\">\r\n              系统文件管理器\r\n            </button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 下载进度对话框 -->\r\n    <view class=\"modal-overlay\" v-if=\"showDownloadProgress\" @click.stop>\r\n      <view class=\"modal-content\">\r\n        <view class=\"progress-header\">\r\n          <text class=\"progress-title\">正在下载</text>\r\n        </view>\r\n        <view class=\"progress-body\">\r\n          <view class=\"progress-bar\">\r\n            <view class=\"progress-fill\" :style=\"{ width: downloadProgress + '%' }\"></view>\r\n          </view>\r\n          <text class=\"progress-text\">{{ downloadProgress }}%</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 上传结果对话框 -->\r\n    <view class=\"modal-overlay\" v-if=\"showUploadResult\" @click=\"hideUploadResult\">\r\n      <view class=\"modal-content upload-result\" @click.stop>\r\n        <view class=\"result-header\">\r\n          <text class=\"result-title\">{{ uploadResultData.msg }}</text>\r\n        </view>\r\n        <view class=\"result-body\">\r\n          <text class=\"result-label\">文件地址：</text>\r\n          <text class=\"result-url\" @click=\"copyUrl\">{{ uploadResultData.url }}</text>\r\n\r\n          <text class=\"result-label\">取件码：</text>\r\n          <text class=\"result-code\" @click=\"copyCode\">{{ uploadResultData.gcode }}</text>\r\n\r\n          <text class=\"result-label\">文件二维码：</text>\r\n          <image class=\"qr-code\" :src=\"uploadResultData.qrCode\" mode=\"aspectFit\"></image>\r\n        </view>\r\n        <view class=\"result-actions\">\r\n          <button class=\"result-btn\" @click=\"saveQRCode\">保存二维码</button>\r\n          <button class=\"result-btn\" @click=\"copyCode\">复制取件码</button>\r\n          <button class=\"result-btn primary\" @click=\"hideUploadResult\">确定</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载中对话框 -->\r\n    <view class=\"modal-overlay\" v-if=\"showLoading\" @click.stop>\r\n      <view class=\"loading-modal\">\r\n        <view class=\"loading-spinner\"></view>\r\n        <text class=\"loading-text\">{{ loadingText }}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getFile, uploadFile, downloadFile } from '@/utils/api.js'\r\nimport { saveUploadRecord, getNoAdStatus } from '@/utils/storage.js'\r\nimport { showToast, showLoading, hideLoading, showConfirm, validatePickupCode, copyToClipboard } from '@/utils/common.js'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 对话框显示状态\r\n      showPickup: false,\r\n      showSend: false,\r\n      showDownloadProgress: false,\r\n      showUploadResult: false,\r\n      showLoading: false,\r\n\r\n      // 取件相关\r\n      pickupCode: '',\r\n\r\n      // 下载进度\r\n      downloadProgress: 0,\r\n\r\n      // 上传结果\r\n      uploadResultData: {\r\n        msg: '',\r\n        url: '',\r\n        gcode: '',\r\n        qrCode: ''\r\n      },\r\n\r\n      // 加载文本\r\n      loadingText: '请稍后...'\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isValidPickupCode() {\r\n      return validatePickupCode(this.pickupCode)\r\n    }\r\n  },\r\n\r\n  onLoad() {\r\n    this.checkForUpdates()\r\n    this.checkDonateStatus()\r\n  },\r\n\r\n  methods: {\r\n    // 显示取件对话框\r\n    showPickupDialog() {\r\n      this.showPickup = true\r\n      this.pickupCode = ''\r\n    },\r\n\r\n    // 隐藏取件对话框\r\n    hidePickupDialog() {\r\n      this.showPickup = false\r\n      this.pickupCode = ''\r\n    },\r\n\r\n    // 显示发件对话框\r\n    showSendDialog() {\r\n      // 检查免广告状态\r\n      const noAd = getNoAdStatus()\r\n      if (!noAd) {\r\n        // 这里可以显示广告\r\n        console.log('显示广告')\r\n      }\r\n      this.showSend = true\r\n    },\r\n\r\n    // 隐藏发件对话框\r\n    hideSendDialog() {\r\n      this.showSend = false\r\n    },\r\n\r\n    // 获取文件\r\n    async getFile() {\r\n      if (!this.isValidPickupCode) {\r\n        showToast('请输入完整的8位取件码')\r\n        return\r\n      }\r\n\r\n      this.showLoading = true\r\n      this.loadingText = '请稍后'\r\n\r\n      try {\r\n        const result = await getFile(this.pickupCode)\r\n\r\n        if (result.status === 1) {\r\n          // 获取成功，开始下载\r\n          const downloadUrl = `https://file.8845.top${result.durl}`\r\n          const fileName = result.wjm\r\n\r\n          this.hidePickupDialog()\r\n          this.showLoading = false\r\n          this.showDownloadProgress = true\r\n          this.downloadProgress = 0\r\n\r\n          await this.downloadFileWithProgress(downloadUrl, fileName)\r\n        } else {\r\n          showToast(result.msg || '取件失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取文件失败:', error)\r\n        showToast('连接超时')\r\n      } finally {\r\n        this.showLoading = false\r\n      }\r\n    },\r\n\r\n    // 下载文件并显示进度\r\n    async downloadFileWithProgress(url, fileName) {\r\n      try {\r\n        // 模拟下载进度\r\n        const progressInterval = setInterval(() => {\r\n          if (this.downloadProgress < 90) {\r\n            this.downloadProgress += Math.random() * 10\r\n          } else if (this.downloadProgress < 100) {\r\n            this.downloadProgress += 1\r\n          } else {\r\n            clearInterval(progressInterval)\r\n          }\r\n        }, 200)\r\n\r\n        // 实际下载文件\r\n        const filePath = await downloadFile(url, fileName)\r\n\r\n        clearInterval(progressInterval)\r\n        this.downloadProgress = 100\r\n\r\n        setTimeout(() => {\r\n          this.showDownloadProgress = false\r\n          showToast('下载成功 已保存至 根目录/下载/文件快递/')\r\n        }, 500)\r\n\r\n      } catch (error) {\r\n        console.error('下载失败:', error)\r\n        this.showDownloadProgress = false\r\n        showToast('下载失败')\r\n      }\r\n    },\r\n\r\n    // 打开自制文件管理器\r\n    openCustomFileManager() {\r\n      this.hideSendDialog()\r\n      uni.navigateTo({\r\n        url: '/pages/fileManager/fileManager'\r\n      })\r\n    },\r\n\r\n    // 打开系统文件管理器\r\n    openSystemFileManager() {\r\n      this.hideSendDialog()\r\n\r\n      // 在uni-app x中使用不同的文件选择API\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n      uni.chooseImage({\r\n        count: 1,\r\n        sourceType: ['album'],\r\n        success: (res) => {\r\n          if (res.tempFilePaths && res.tempFilePaths.length > 0) {\r\n            const filePath = res.tempFilePaths[0]\r\n            const fileName = filePath.split('/').pop() || 'unknown'\r\n            this.uploadSelectedFile(filePath, fileName)\r\n          }\r\n        },\r\n        fail: (error) => {\r\n          console.error('选择文件失败:', error)\r\n          showToast('选择文件失败，请使用自制文件管理器')\r\n        }\r\n      })\r\n\r\n    },\r\n\r\n    // 上传选中的文件\r\n    async uploadSelectedFile(file, fileName) {\r\n      const confirmed = await showConfirm(`是否上传文件：${fileName}？`)\r\n      if (!confirmed) return\r\n\r\n      this.showLoading = true\r\n      this.loadingText = '上传中'\r\n\r\n      try {\r\n        // 处理不同类型的文件输入\r\n        let filePath = file\r\n        if (typeof file === 'object' && file.path) {\r\n          filePath = file.path\r\n        } else if (typeof file === 'object' && file instanceof File) {\r\n          // Web环境下的File对象\r\n          filePath = file\r\n        }\r\n\r\n        const result = await uploadFile(filePath)\r\n\r\n        if (result.status === 1) {\r\n          // 上传成功\r\n          const gcode = result.get_code\r\n          const fileUrl = `https://file.8845.top/file/${gcode}/${fileName}`\r\n\r\n          // 生成二维码（这里使用简单的二维码API）\r\n          const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(fileUrl)}`\r\n\r\n          this.uploadResultData = {\r\n            msg: result.msg,\r\n            url: fileUrl,\r\n            gcode: gcode,\r\n            qrCode: qrCodeUrl\r\n          }\r\n\r\n          // 保存上传记录\r\n          saveUploadRecord({\r\n            filename: fileName,\r\n            gcode: gcode,\r\n            url: fileUrl\r\n          })\r\n\r\n          this.showLoading = false\r\n          this.showUploadResult = true\r\n\r\n        } else {\r\n          showToast(result.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        showToast('连接超时')\r\n      } finally {\r\n        this.showLoading = false\r\n      }\r\n    },\r\n\r\n    // 隐藏上传结果对话框\r\n    hideUploadResult() {\r\n      this.showUploadResult = false\r\n    },\r\n\r\n    // 复制URL\r\n    async copyUrl() {\r\n      await copyToClipboard(this.uploadResultData.url)\r\n    },\r\n\r\n    // 复制取件码\r\n    async copyCode() {\r\n      await copyToClipboard(this.uploadResultData.gcode)\r\n    },\r\n\r\n    // 保存二维码\r\n    saveQRCode() {\r\n      // 这里实现保存二维码的逻辑\r\n      showToast('已保存至 根目录/下载/文件快递/二维码/')\r\n    },\r\n\r\n    // 显示捐赠页面\r\n    showDonate() {\r\n      uni.navigateTo({\r\n        url: '/pages/donate/donate'\r\n      })\r\n    },\r\n\r\n    // 显示上传记录\r\n    showUploadRecord() {\r\n      uni.navigateTo({\r\n        url: '/pages/uploadRecord/uploadRecord'\r\n      })\r\n    },\r\n\r\n    // 检查更新\r\n    async checkForUpdates() {\r\n      // 这里实现检查更新的逻辑\r\n      console.log('检查更新')\r\n    },\r\n\r\n    // 检查捐赠状态\r\n    async checkDonateStatus() {\r\n      // 这里实现检查捐赠状态的逻辑\r\n      console.log('检查捐赠状态')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n  min-height: 100vh;\r\n  background-color: #ffffff;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 头部样式 */\r\n.header {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 60px 20px 40px;\r\n  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);\r\n}\r\n\r\n.logo {\r\n  width: 80px;\r\n  height: 80px;\r\n  margin-bottom: 20px;\r\n  border-radius: 15px;\r\n}\r\n\r\n.app-title {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  color: #ffffff;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n  flex: 1;\r\n  padding: 40px 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n/* 功能卡片 */\r\n.function-card {\r\n  background-color: #ffffff;\r\n  border-radius: 15px;\r\n  padding: 30px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  border: 2px solid #f0f0f0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.function-card:active {\r\n  transform: scale(0.98);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.send-card {\r\n  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);\r\n  border: none;\r\n}\r\n\r\n.card-content {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #1f999b;\r\n}\r\n\r\n.send-title {\r\n  color: #ffffff;\r\n}\r\n\r\n/* 底部操作区域 */\r\n.bottom-actions {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  padding: 20px;\r\n  margin-top: auto;\r\n}\r\n\r\n.action-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.action-text {\r\n  font-size: 13px;\r\n  font-weight: bold;\r\n  color: #b8b8b8;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.action-line {\r\n  width: 50px;\r\n  height: 2px;\r\n  background-color: #b8b8b8;\r\n}\r\n\r\n/* 模态框样式 */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(59, 0, 0, 0.6);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-content {\r\n  background-color: #ffffff;\r\n  border-radius: 15px;\r\n  margin: 20px;\r\n  max-width: 90%;\r\n  width: 320px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 20px 20px 10px;\r\n}\r\n\r\n.modal-icon {\r\n  width: 80px;\r\n  height: 80px;\r\n}\r\n\r\n.modal-body {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.modal-title {\r\n  font-size: 17px;\r\n  font-weight: bold;\r\n  color: #1f999b;\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 输入框样式 */\r\n.input-container {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.pickup-input {\r\n  width: 100%;\r\n  height: 45px;\r\n  border: none;\r\n  background-color: #f8f8f8;\r\n  border-radius: 10px;\r\n  padding: 0 15px;\r\n  font-size: 15px;\r\n  text-align: center;\r\n  color: #1f999b;\r\n}\r\n\r\n.pickup-input::placeholder {\r\n  color: #bcbcbc;\r\n}\r\n\r\n/* 按钮样式 */\r\n.modal-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.modal-btn {\r\n  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);\r\n  color: #ffffff;\r\n  border: none;\r\n  border-radius: 10px;\r\n  padding: 12px 30px;\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n}\r\n\r\n.modal-btn:disabled {\r\n  background: #cccccc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.file-options {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n}\r\n\r\n.file-option-btn {\r\n  background-color: #ffffff;\r\n  border: 2px solid #1f999b;\r\n  color: #1f999b;\r\n  border-radius: 10px;\r\n  padding: 12px 20px;\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n}\r\n\r\n.file-option-btn.primary {\r\n  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);\r\n  color: #ffffff;\r\n  border: none;\r\n}\r\n\r\n/* 进度条样式 */\r\n.progress-header {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.progress-title {\r\n  font-size: 17px;\r\n  font-weight: bold;\r\n  color: #1f999b;\r\n}\r\n\r\n.progress-body {\r\n  padding: 0 20px 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.progress-bar {\r\n  width: 100%;\r\n  height: 6px;\r\n  background-color: #f0f0f0;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #1f999b 0%, #16a085 100%);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 12px;\r\n  color: #999999;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 上传结果样式 */\r\n.upload-result {\r\n  max-height: 80vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.result-header {\r\n  padding: 20px;\r\n  text-align: center;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.result-title {\r\n  font-size: 17px;\r\n  font-weight: bold;\r\n  color: #1f999b;\r\n}\r\n\r\n.result-body {\r\n  padding: 20px;\r\n}\r\n\r\n.result-label {\r\n  font-size: 14px;\r\n  color: #1f999b;\r\n  font-weight: bold;\r\n  display: block;\r\n  margin-top: 15px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.result-label:first-child {\r\n  margin-top: 0;\r\n}\r\n\r\n.result-url, .result-code {\r\n  font-size: 14px;\r\n  color: #f98b8b;\r\n  font-weight: bold;\r\n  word-break: break-all;\r\n  cursor: pointer;\r\n  padding: 8px;\r\n  background-color: #f8f8f8;\r\n  border-radius: 5px;\r\n}\r\n\r\n.qr-code {\r\n  width: 120px;\r\n  height: 120px;\r\n  margin: 10px 0;\r\n  border-radius: 5px;\r\n}\r\n\r\n.result-actions {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  padding: 20px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.result-btn {\r\n  background-color: #ffffff;\r\n  border: 1px solid #1f999b;\r\n  color: #1f999b;\r\n  border-radius: 8px;\r\n  padding: 8px 12px;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  flex: 1;\r\n  margin: 0 5px;\r\n}\r\n\r\n.result-btn.primary {\r\n  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);\r\n  color: #ffffff;\r\n  border: none;\r\n}\r\n\r\n/* 加载中样式 */\r\n.loading-modal {\r\n  background-color: #ffffff;\r\n  border-radius: 10px;\r\n  padding: 30px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  min-width: 150px;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 30px;\r\n  height: 30px;\r\n  border: 3px solid #f3f3f3;\r\n  border-top: 3px solid #1f999b;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.loading-text {\r\n  font-size: 15px;\r\n  color: #1f999b;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 主页面样式 */\r\n.container {\r\n  min-height: 100vh;\r\n  background-color: #ffffff;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 头部样式 */\r\n.header {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 60px 20px 40px;\r\n  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);\r\n}\r\n\r\n.logo {\r\n  width: 80px;\r\n  height: 80px;\r\n  margin-bottom: 20px;\r\n  border-radius: 15px;\r\n}\r\n\r\n.app-title {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  color: #ffffff;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n  flex: 1;\r\n  padding: 40px 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n/* 功能卡片 */\r\n.function-card {\r\n  background-color: #ffffff;\r\n  border-radius: 15px;\r\n  padding: 30px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  border: 2px solid #f0f0f0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.function-card:active {\r\n  transform: scale(0.98);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.send-card {\r\n  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);\r\n  border: none;\r\n}\r\n\r\n.card-content {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #1f999b;\r\n}\r\n\r\n.send-title {\r\n  color: #ffffff;\r\n}\r\n\r\n/* 底部操作区域 */\r\n.bottom-actions {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  padding: 20px;\r\n  margin-top: auto;\r\n}\r\n\r\n.action-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.action-text {\r\n  font-size: 13px;\r\n  font-weight: bold;\r\n  color: #b8b8b8;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.action-line {\r\n  width: 50px;\r\n  height: 2px;\r\n  background-color: #b8b8b8;\r\n}\r\n</style>\r\n"], "mappings": ";AAoIA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAExH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAElB,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAEd,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAEnB,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACX,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACrB,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACrB,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC;MACP;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEvB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;;UAExB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACzB;IACF,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5C,CAAC,CAAC,EAAE;QACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UAC5C,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC3B,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;QACF,CAAC,EAAE,CAAC,CAAC,CAAC;;QAEN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;QAE1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAER,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;IACF,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC;IACH,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;MAezB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;YACrD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C;QACF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B;MACF,CAAC;;IAEH,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEvB,CAAC,CAAC,EAAE;QACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAChB;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAExC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UACvB,CAAC,EAAE,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEhE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE/G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC;;UAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;QAE7B,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACzB;IACF,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC;IACH,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC;IACH,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB;EACF;AACF;;;;;;SAnaE,mBA+HO,mBA/HD,KAAK,EAAC,WAAW;IAErB,mBAGO,mBAHD,KAAK,EAAC,QAAQ;MAClB,mBAA2E;QAApE,KAAK,EAAC,MAAM;QAAC,GAAG,EAAC,yBAAyB;QAAC,IAAI,EAAC,WAAW;;MAClE,mBAAqC,mBAA/B,KAAK,EAAC,WAAW,KAAC,QAAM;;IAIhC,mBAcO,mBAdD,KAAK,EAAC,cAAc;MAExB,mBAIO;QAJD,KAAK,EAAC,eAAe;QAAE,OAAK,EAAE,qBAAgB;;QAClD,mBAEO,mBAFD,KAAK,EAAC,cAAc;UACxB,mBAAkC,mBAA5B,KAAK,EAAC,YAAY,KAAC,IAAE;;;MAK/B,mBAIO;QAJD,KAAK,EAAC,yBAAyB;QAAE,OAAK,EAAE,mBAAc;;QAC1D,mBAEO,mBAFD,KAAK,EAAC,cAAc;UACxB,mBAA6C,mBAAvC,KAAK,EAAC,uBAAuB,KAAC,IAAE;;;;IAM5C,mBASO,mBATD,KAAK,EAAC,gBAAgB;MAC1B,mBAGO;QAHD,KAAK,EAAC,aAAa;QAAE,OAAK,EAAE,eAAU;;QAC1C,mBAAsC,mBAAhC,KAAK,EAAC,aAAa,KAAC,OAAK;QAC/B,mBAAiC,mBAA3B,KAAK,EAAC,aAAa;;MAE3B,mBAGO;QAHD,KAAK,EAAC,aAAa;QAAE,OAAK,EAAE,qBAAgB;;QAChD,mBAAqC,mBAA/B,KAAK,EAAC,aAAa,KAAC,MAAI;QAC9B,mBAAiC,mBAA3B,KAAK,EAAC,aAAa;;;WAKK,eAAU;QAA5C,mBAsBO;;UAtBD,KAAK,EAAC,eAAe;UAAoB,OAAK,EAAE,qBAAgB;;UACpE,mBAoBO;YApBD,KAAK,EAAC,eAAe;YAAE,OAAK,gBAAN,QAAW;;YACrC,mBAEO,mBAFD,KAAK,EAAC,cAAc;cACxB,mBAAwF;gBAAjF,KAAK,EAAC,YAAY;gBAAC,GAAG,EAAC,gCAAgC;gBAAC,IAAI,EAAC,WAAW;;;YAEjF,mBAeO,mBAfD,KAAK,EAAC,YAAY;cACtB,mBAQO,mBARD,KAAK,EAAC,iBAAiB;gBAC3B,mBAME;kBALA,KAAK,EAAC,cAAc;8BACX,eAAU;qDAAV,eAAU;kBACnB,WAAW,EAAC,UAAU;kBACtB,SAAS,EAAC,GAAG;kBACb,IAAI,EAAC,QAAQ;;;cAGjB,mBAIO,mBAJD,KAAK,EAAC,eAAe;gBACzB,mBAES;kBAFD,KAAK,EAAC,mBAAmB;kBAAE,OAAK,EAAE,YAAO;kBAAG,QAAQ,GAAG,sBAAiB;oBAAE,MAElF;;;;;;WAO0B,aAAQ;QAA1C,mBAiBO;;UAjBD,KAAK,EAAC,eAAe;UAAkB,OAAK,EAAE,mBAAc;;UAChE,mBAeO;YAfD,KAAK,EAAC,eAAe;YAAE,OAAK,gBAAN,QAAW;;YACrC,mBAEO,mBAFD,KAAK,EAAC,cAAc;cACxB,mBAAsF;gBAA/E,KAAK,EAAC,YAAY;gBAAC,GAAG,EAAC,8BAA8B;gBAAC,IAAI,EAAC,WAAW;;;YAE/E,mBAUO,mBAVD,KAAK,EAAC,YAAY;cACtB,mBAAqC,mBAA/B,KAAK,EAAC,aAAa,KAAC,MAAI;cAC9B,mBAOO,mBAPD,KAAK,EAAC,cAAc;gBACxB,mBAES;kBAFD,KAAK,EAAC,iBAAiB;kBAAE,OAAK,EAAE,0BAAqB;oBAAE,WAE/D;gBACA,mBAES;kBAFD,KAAK,EAAC,yBAAyB;kBAAE,OAAK,EAAE,0BAAqB;oBAAE,WAEvE;;;;;;WAO0B,yBAAoB;QAAtD,mBAYO;;UAZD,KAAK,EAAC,eAAe;UAA8B,OAAK,gBAAN,QAAW;;UACjE,mBAUO,mBAVD,KAAK,EAAC,eAAe;YACzB,mBAEO,mBAFD,KAAK,EAAC,iBAAiB;cAC3B,mBAAwC,mBAAlC,KAAK,EAAC,gBAAgB,KAAC,MAAI;;YAEnC,mBAKO,mBALD,KAAK,EAAC,eAAe;cACzB,mBAEO,mBAFD,KAAK,EAAC,cAAc;gBACxB,mBAA8E;kBAAxE,KAAK,EAAC,eAAe;kBAAE,KAAK,iBAAE,gDAAiC;;;cAEvE,mBAA0D,mBAApD,KAAK,EAAC,eAAe,qBAAI,qBAAgB,IAAG,GAAC;;;;;WAMvB,qBAAgB;QAAlD,mBAqBO;;UArBD,KAAK,EAAC,eAAe;UAA0B,OAAK,EAAE,qBAAgB;;UAC1E,mBAmBO;YAnBD,KAAK,EAAC,6BAA6B;YAAE,OAAK,gBAAN,QAAW;;YACnD,mBAEO,mBAFD,KAAK,EAAC,eAAe;cACzB,mBAA4D,mBAAtD,KAAK,EAAC,cAAc,qBAAI,qBAAgB,CAAC,GAAG;;YAEpD,mBASO,mBATD,KAAK,EAAC,aAAa;cACvB,mBAAuC,mBAAjC,KAAK,EAAC,cAAc,KAAC,OAAK;cAChC,mBAA2E;gBAArE,KAAK,EAAC,YAAY;gBAAE,OAAK,EAAE,YAAO;kCAAK,qBAAgB,CAAC,GAAG;cAEjE,mBAAsC,mBAAhC,KAAK,EAAC,cAAc,KAAC,MAAI;cAC/B,mBAA+E;gBAAzE,KAAK,EAAC,aAAa;gBAAE,OAAK,EAAE,aAAQ;kCAAK,qBAAgB,CAAC,KAAK;cAErE,mBAAwC,mBAAlC,KAAK,EAAC,cAAc,KAAC,QAAM;cACjC,mBAA+E;gBAAxE,KAAK,EAAC,SAAS;gBAAE,GAAG,EAAE,qBAAgB,CAAC,MAAM;gBAAE,IAAI,EAAC,WAAW;;;YAExE,mBAIO,mBAJD,KAAK,EAAC,gBAAgB;cAC1B,mBAA6D;gBAArD,KAAK,EAAC,YAAY;gBAAE,OAAK,EAAE,eAAU;kBAAE,OAAK;cACpD,mBAA2D;gBAAnD,KAAK,EAAC,YAAY;gBAAE,OAAK,EAAE,aAAQ;kBAAE,OAAK;cAClD,mBAAwE;gBAAhE,KAAK,EAAC,oBAAoB;gBAAE,OAAK,EAAE,qBAAgB;kBAAE,IAAE;;;;;WAMnC,gBAAW;QAA7C,mBAKO;;UALD,KAAK,EAAC,eAAe;UAAqB,OAAK,gBAAN,QAAW;;UACxD,mBAGO,mBAHD,KAAK,EAAC,eAAe;YACzB,mBAAqC,mBAA/B,KAAK,EAAC,iBAAiB;YAC7B,mBAAmD,mBAA7C,KAAK,EAAC,cAAc,qBAAI,gBAAW"}