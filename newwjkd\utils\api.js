// API配置和网络请求工具
const BASE_URL = 'https://file.8845.top'

// 网络请求封装
export const request = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: BASE_URL + url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/x-www-form-urlencoded',
        ...options.header
      },
      timeout: 30000,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`))
        }
      },
      fail: (err) => {
        reject(new Error('网络连接失败'))
      }
    })
  })
}

// 文件上传
export const uploadFile = (filePath) => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: BASE_URL + '/api/upfile.php',
      filePath: filePath,
      name: 'file',
      timeout: 60000,
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          resolve(data)
        } catch (e) {
          reject(new Error('响应数据解析失败'))
        }
      },
      fail: (err) => {
        reject(new Error('文件上传失败'))
      }
    })
  })
}

// 文件下载
export const downloadFile = (url, fileName) => {
  return new Promise((resolve, reject) => {
    uni.downloadFile({
      url: url,
      timeout: 60000,
      success: (res) => {
        if (res.statusCode === 200) {
          // 保存到本地
          uni.saveFile({
            tempFilePath: res.tempFilePath,
            success: (saveRes) => {
              resolve(saveRes.savedFilePath)
            },
            fail: (err) => {
              reject(new Error('文件保存失败'))
            }
          })
        } else {
          reject(new Error('文件下载失败'))
        }
      },
      fail: (err) => {
        reject(new Error('文件下载失败'))
      }
    })
  })
}

// 获取文件
export const getFile = (code) => {
  return request('/api/downfile.php', {
    method: 'POST',
    data: { gcode: code }
  })
}

// 获取捐赠信息
export const getDonateInfo = () => {
  return request('/api/get_juan.php')
}

// 获取版本更新信息
export const getUpdateInfo = () => {
  return request('/api/get_up.php')
}

// 获取公告信息
export const getNoticeInfo = () => {
  return request('/api/get_gg.php')
}
