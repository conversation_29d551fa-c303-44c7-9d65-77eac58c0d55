
import { getDonateInfo } from '@/utils/api.js'
import { showToast } from '@/utils/common.js'

const __sfc__ = defineComponent({
  data() {
    return {
      showQRDialog: false,
      currentPayMethod: '',
      currentQRCode: '',
      donateRecords: [],
      qrCodes: {
        alipay: '/static/images/alipay-qr.png',
        wechat: '/static/images/wechat-qr.png'
      }
    }
  },
  
  onLoad() {
    this.loadDonateInfo()
  },
  
  methods: {
    // 加载捐赠信息
    async loadDonateInfo() {
      try {
        const result = await getDonateInfo()
        if (result.status === 1) {
          this.donateRecords = result.records || []
          // 更新二维码地址
          if (result.qrCodes) {
            this.qrCodes = { ...this.qrCodes, ...result.qrCodes }
          }
        }
      } catch (error) {
        console.error('加载捐赠信息失败:', error, " at pages/donate/donate.uvue:112")
      }
    },
    
    // 显示二维码
    showQRCode(method) {
      this.currentPayMethod = method
      this.currentQRCode = this.qrCodes[method]
      this.showQRDialog = true
    },
    
    // 隐藏二维码对话框
    hideQRDialog() {
      this.showQRDialog = false
      this.currentPayMethod = ''
      this.currentQRCode = ''
    },
    
    // 保存二维码
    saveQRCode() {
      // 这里实现保存二维码到相册的逻辑
      uni.saveImageToPhotosAlbum({
        filePath: this.currentQRCode,
        success: () => {
          showToast('二维码已保存到相册')
        },
        fail: () => {
          showToast('保存失败，请手动截图保存')
        }
      })
    }
  }
})

export default __sfc__
function GenPagesDonateDonateRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
  return createElementVNode("view", utsMapOf({ class: "container" }), [
    createElementVNode("view", utsMapOf({ class: "header" }), [
      createElementVNode("text", utsMapOf({ class: "header-title" }), "捐赠支持"),
      createElementVNode("text", utsMapOf({ class: "header-subtitle" }), "您的支持是我们前进的动力")
    ]),
    createElementVNode("view", utsMapOf({ class: "donate-info" }), [
      createElementVNode("view", utsMapOf({ class: "info-card" }), [
        createElementVNode("text", utsMapOf({ class: "info-title" }), "为什么需要捐赠？"),
        createElementVNode("text", utsMapOf({ class: "info-content" }), " 文件快递服务需要服务器维护费用、域名费用等成本支出。您的捐赠将帮助我们： • 维持服务器稳定运行 • 提升文件传输速度 • 开发更多实用功能 • 提供更好的用户体验 ")
      ])
    ]),
    createElementVNode("view", utsMapOf({ class: "donate-methods" }), [
      createElementVNode("text", utsMapOf({ class: "section-title" }), "选择捐赠方式"),
      createElementVNode("view", utsMapOf({
        class: "method-card",
        onClick: () => {_ctx.showQRCode('alipay')}
      }), [
        createElementVNode("image", utsMapOf({
          class: "method-icon",
          src: "/static/images/alipay-icon.png",
          mode: "aspectFit"
        })),
        createElementVNode("view", utsMapOf({ class: "method-info" }), [
          createElementVNode("text", utsMapOf({ class: "method-name" }), "支付宝"),
          createElementVNode("text", utsMapOf({ class: "method-desc" }), "扫码支付，安全便捷")
        ]),
        createElementVNode("text", utsMapOf({ class: "method-arrow" }), ">")
      ], 8 /* PROPS */, ["onClick"]),
      createElementVNode("view", utsMapOf({
        class: "method-card",
        onClick: () => {_ctx.showQRCode('wechat')}
      }), [
        createElementVNode("image", utsMapOf({
          class: "method-icon",
          src: "/static/images/wechat-icon.png",
          mode: "aspectFit"
        })),
        createElementVNode("view", utsMapOf({ class: "method-info" }), [
          createElementVNode("text", utsMapOf({ class: "method-name" }), "微信支付"),
          createElementVNode("text", utsMapOf({ class: "method-desc" }), "微信扫码，快速支付")
        ]),
        createElementVNode("text", utsMapOf({ class: "method-arrow" }), ">")
      ], 8 /* PROPS */, ["onClick"])
    ]),
    _ctx.donateRecords.length > 0
      ? createElementVNode("view", utsMapOf({
          key: 0,
          class: "donate-records"
        }), [
          createElementVNode("text", utsMapOf({ class: "section-title" }), "感谢以下用户的支持"),
          createElementVNode("scroll-view", utsMapOf({
            class: "records-list",
            "scroll-y": "true"
          }), [
            createElementVNode(Fragment, null, RenderHelpers.renderList(_ctx.donateRecords, (record, index, __index, _cached): any => {
              return createElementVNode("view", utsMapOf({
                class: "record-item",
                key: index
              }), [
                createElementVNode("text", utsMapOf({ class: "record-name" }), toDisplayString(record.name), 1 /* TEXT */),
                createElementVNode("text", utsMapOf({ class: "record-amount" }), "¥" + toDisplayString(record.amount), 1 /* TEXT */),
                createElementVNode("text", utsMapOf({ class: "record-time" }), toDisplayString(record.time), 1 /* TEXT */)
              ])
            }), 128 /* KEYED_FRAGMENT */)
          ])
        ])
      : createCommentVNode("v-if", true),
    isTrue(_ctx.showQRDialog)
      ? createElementVNode("view", utsMapOf({
          key: 1,
          class: "modal-overlay",
          onClick: _ctx.hideQRDialog
        }), [
          createElementVNode("view", utsMapOf({
            class: "modal-content",
            onClick: withModifiers(() => {}, ["stop"])
          }), [
            createElementVNode("view", utsMapOf({ class: "modal-header" }), [
              createElementVNode("text", utsMapOf({ class: "modal-title" }), toDisplayString(_ctx.currentPayMethod === 'alipay' ? '支付宝' : '微信') + "捐赠", 1 /* TEXT */)
            ]),
            createElementVNode("view", utsMapOf({ class: "modal-body" }), [
              createElementVNode("image", utsMapOf({
                class: "qr-code",
                src: _ctx.currentQRCode,
                mode: "aspectFit"
              }), null, 8 /* PROPS */, ["src"]),
              createElementVNode("text", utsMapOf({ class: "qr-desc" }), "请使用" + toDisplayString(_ctx.currentPayMethod === 'alipay' ? '支付宝' : '微信') + "扫描二维码", 1 /* TEXT */)
            ]),
            createElementVNode("view", utsMapOf({ class: "modal-actions" }), [
              createElementVNode("button", utsMapOf({
                class: "modal-btn",
                onClick: _ctx.saveQRCode
              }), "保存二维码", 8 /* PROPS */, ["onClick"]),
              createElementVNode("button", utsMapOf({
                class: "modal-btn primary",
                onClick: _ctx.hideQRDialog
              }), "关闭", 8 /* PROPS */, ["onClick"])
            ])
          ], 8 /* PROPS */, ["onClick"])
        ], 8 /* PROPS */, ["onClick"])
      : createCommentVNode("v-if", true)
  ])
}
const GenPagesDonateDonateStyles = [utsMapOf([["container", padStyleMapOf(utsMapOf([["backgroundColor", "#f8f8f8"]]))], ["header", padStyleMapOf(utsMapOf([["backgroundImage", "linear-gradient(135deg, #1f999b 0%, #16a085 100%)"], ["paddingTop", 40], ["paddingRight", 20], ["paddingBottom", 30], ["paddingLeft", 20], ["color", "#ffffff"], ["textAlign", "center"]]))], ["header-title", padStyleMapOf(utsMapOf([["fontSize", 24], ["fontWeight", "bold"], ["marginBottom", 8]]))], ["header-subtitle", padStyleMapOf(utsMapOf([["fontSize", 14], ["opacity", 0.9]]))], ["donate-info", padStyleMapOf(utsMapOf([["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20]]))], ["info-card", padStyleMapOf(utsMapOf([["backgroundColor", "#ffffff"], ["borderRadius", 12], ["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20], ["boxShadow", "0 2px 8px rgba(0, 0, 0, 0.1)"]]))], ["info-title", padStyleMapOf(utsMapOf([["fontSize", 18], ["fontWeight", "bold"], ["color", "#333333"], ["marginBottom", 12]]))], ["info-content", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#666666"], ["lineHeight", 1.6]]))], ["donate-methods", padStyleMapOf(utsMapOf([["paddingTop", 0], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20]]))], ["section-title", padStyleMapOf(utsMapOf([["fontSize", 16], ["fontWeight", "bold"], ["color", "#333333"], ["marginBottom", 15]]))], ["method-card", padStyleMapOf(utsMapOf([["backgroundColor", "#ffffff"], ["borderRadius", 12], ["paddingTop", 15], ["paddingRight", 15], ["paddingBottom", 15], ["paddingLeft", 15], ["marginBottom", 12], ["display", "flex"], ["alignItems", "center"], ["boxShadow", "0 2px 8px rgba(0, 0, 0, 0.1)"], ["cursor", "pointer"], ["transitionProperty", "transform"], ["transitionDuration", "0.2s"], ["transitionTimingFunction", "ease"], ["transform:active", "scale(0.98)"]]))], ["method-icon", padStyleMapOf(utsMapOf([["width", 40], ["height", 40], ["marginRight", 15]]))], ["method-info", padStyleMapOf(utsMapOf([["flex", 1]]))], ["method-name", padStyleMapOf(utsMapOf([["fontSize", 16], ["fontWeight", "bold"], ["color", "#333333"], ["marginBottom", 4]]))], ["method-desc", padStyleMapOf(utsMapOf([["fontSize", 12], ["color", "#999999"]]))], ["method-arrow", padStyleMapOf(utsMapOf([["fontSize", 18], ["color", "#cccccc"], ["fontWeight", "bold"]]))], ["donate-records", padStyleMapOf(utsMapOf([["paddingTop", 0], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20]]))], ["records-list", padStyleMapOf(utsMapOf([["backgroundColor", "#ffffff"], ["borderRadius", 12], ["maxHeight", 200], ["boxShadow", "0 2px 8px rgba(0, 0, 0, 0.1)"]]))], ["record-item", padStyleMapOf(utsMapOf([["display", "flex"], ["alignItems", "center"], ["paddingTop", 12], ["paddingRight", 15], ["paddingBottom", 12], ["paddingLeft", 15], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#f0f0f0"], ["borderBottomWidth:last-child", "medium"], ["borderBottomStyle:last-child", "none"], ["borderBottomColor:last-child", "#000000"]]))], ["record-name", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#333333"], ["flex", 1]]))], ["record-amount", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#1f999b"], ["fontWeight", "bold"], ["marginRight", 15]]))], ["record-time", padStyleMapOf(utsMapOf([["fontSize", 12], ["color", "#999999"]]))], ["modal-overlay", padStyleMapOf(utsMapOf([["position", "fixed"], ["top", 0], ["left", 0], ["right", 0], ["bottom", 0], ["backgroundColor", "rgba(0,0,0,0.5)"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["zIndex", 1000]]))], ["modal-content", padStyleMapOf(utsMapOf([["backgroundColor", "#ffffff"], ["borderRadius", 15], ["marginTop", 20], ["marginRight", 20], ["marginBottom", 20], ["marginLeft", 20], ["maxWidth", 320], ["width", "90%"], ["overflow", "hidden"]]))], ["modal-header", padStyleMapOf(utsMapOf([["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20], ["textAlign", "center"], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#f0f0f0"]]))], ["modal-title", padStyleMapOf(utsMapOf([["fontSize", 18], ["fontWeight", "bold"], ["color", "#333333"]]))], ["modal-body", padStyleMapOf(utsMapOf([["paddingTop", 30], ["paddingRight", 20], ["paddingBottom", 30], ["paddingLeft", 20], ["textAlign", "center"]]))], ["qr-code", padStyleMapOf(utsMapOf([["width", 200], ["height", 200], ["marginBottom", 15], ["borderRadius", 8]]))], ["qr-desc", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#666666"]]))], ["modal-actions", padStyleMapOf(utsMapOf([["display", "flex"], ["borderTopWidth", 1], ["borderTopStyle", "solid"], ["borderTopColor", "#f0f0f0"]]))], ["modal-btn", utsMapOf([["", utsMapOf([["flex", 1], ["paddingTop", 15], ["paddingRight", 15], ["paddingBottom", 15], ["paddingLeft", 15], ["borderWidth", "medium"], ["borderStyle", "none"], ["borderColor", "#000000"], ["backgroundColor", "#ffffff"], ["fontSize", 16], ["fontWeight", "bold"], ["cursor", "pointer"], ["borderRightWidth", 1], ["borderRightStyle", "solid"], ["borderRightColor", "#f0f0f0"], ["borderRightWidth:last-child", "medium"], ["borderRightStyle:last-child", "none"], ["borderRightColor:last-child", "#000000"], ["backgroundColor:active", "#f8f8f8"]])], [".primary", utsMapOf([["color", "#1f999b"]])]])], ["@TRANSITION", utsMapOf([["method-card", utsMapOf([["property", "transform"], ["duration", "0.2s"], ["timingFunction", "ease"]])]])]])]

//# sourceMappingURL=donate.uvue.map