<template>
  <view class="loading-overlay" v-if="visible" @click="handleOverlayClick">
    <view class="loading-content" @click.stop>
      <view class="loading-spinner" v-if="type === 'spinner'"></view>
      <view class="loading-progress" v-else-if="type === 'progress'">
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: progress + '%' }"></view>
        </view>
        <text class="progress-text">{{ progress }}%</text>
      </view>
      <text class="loading-text">{{ text }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LoadingModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: '加载中...'
    },
    type: {
      type: String,
      default: 'spinner', // spinner | progress
      validator: (value) => ['spinner', 'progress'].includes(value)
    },
    progress: {
      type: Number,
      default: 0,
      validator: (value) => value >= 0 && value <= 100
    },
    maskClosable: {
      type: Boolean,
      default: false
    }
  },
  
  methods: {
    handleOverlayClick() {
      if (this.maskClosable) {
        this.$emit('close')
      }
    }
  }
}
</script>

<style scoped>
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 150px;
  max-width: 80%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1f999b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-progress {
  width: 200px;
  margin-bottom: 20px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1f999b 0%, #16a085 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: #666666;
  text-align: center;
  font-weight: 500;
}

.loading-text {
  font-size: 16px;
  color: #1f999b;
  font-weight: bold;
  text-align: center;
  line-height: 1.4;
}
</style>
