<script lang="uts">
	let firstBackTime = 0
	export default {
		onLaunch: function () {
			console.log('App Launch')
			// 检查应用版本
			this.checkAppVersion()
		},
		onShow: function () {
			console.log('App Show')
		},
		onHide: function () {
			console.log('App Hide')
		},
		// #ifdef APP-ANDROID
		onLastPageBackPress: function () {
			console.log('App LastPageBackPress')
			if (firstBackTime == 0) {
				uni.showToast({
					title: '再按一次退出应用',
					position: 'bottom',
				})
				firstBackTime = Date.now()
				setTimeout(() => {
					firstBackTime = 0
				}, 2000)
			} else if (Date.now() - firstBackTime < 2000) {
				firstBackTime = Date.now()
				uni.exit()
			}
		},
		// #endif
		onExit: function () {
			console.log('App Exit')
		},
		methods: {
			// 检查应用版本
			checkAppVersion() {
				const currentVersion = '2.1.0'
				const savedVersion = uni.getStorageSync('app_version')

				if (savedVersion !== currentVersion) {
					// 版本更新，清理缓存或执行升级逻辑
					console.log('应用版本更新:', savedVersion, '->', currentVersion)
					uni.setStorageSync('app_version', currentVersion)
				}
			}
		}
	}
</script>

<style>
/* 引入全局样式 */
@import url('./static/css/global.css');

/* 每个页面公共css */
page {
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.uni-row {
  flex-direction: row;
}

.uni-column {
  flex-direction: column;
}

/* 全局动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 300ms ease;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active, .slide-up-leave-active {
  transition: transform 300ms ease;
}

.slide-up-enter, .slide-up-leave-to {
  transform: translateY(100%);
}

.scale-enter-active, .scale-leave-active {
  transition: transform 300ms ease;
}

.scale-enter, .scale-leave-to {
  transform: scale(0.8);
}
</style>