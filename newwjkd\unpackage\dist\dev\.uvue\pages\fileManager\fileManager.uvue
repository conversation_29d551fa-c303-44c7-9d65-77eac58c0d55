
import { uploadFile } from '@/utils/api.js'
import { saveUploadRecord } from '@/utils/storage.js'
import { showToast, formatFileSize } from '@/utils/common.js'

const __sfc__ = defineComponent({
  data() {
    return {
      currentPath: '/storage/emulated/0',
      fileList: [] as Array<{name: string, isDirectory: boolean, size: number}>,
      loading: false,
      showConfirmDialog: false,
      selectedFile: {
        name: '',
        isDirectory: false,
        size: 0
      } as {name: string, isDirectory: boolean, size: number}
    }
  },
  
  onLoad() {
    this.loadFileList(this.currentPath)
  },
  
  methods: {
    // 加载文件列表
    async loadFileList(path) {
      this.loading = true
      try {
        // 在实际应用中，这里需要调用原生API获取文件列表
        // 由于uni-app x的限制，这里使用模拟数据
        await this.delay(500)
        
        this.fileList = await this.getFileListFromPath(path)
        this.currentPath = path
      } catch (error) {
        console.error('加载文件列表失败:', error, " at pages/fileManager/fileManager.uvue:102")
        showToast('加载文件列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 模拟获取文件列表（实际项目中需要调用原生API）
    async getFileListFromPath(path: string): Promise<Array<{name: string, isDirectory: boolean, size: number}>> {
      // 这里返回模拟数据，实际项目中需要调用原生文件系统API
      const mockFiles: Array<{name: string, isDirectory: boolean, size: number}> = [
        { name: 'Documents', isDirectory: true, size: 0 },
        { name: 'Download', isDirectory: true, size: 0 },
        { name: 'Pictures', isDirectory: true, size: 0 },
        { name: 'Music', isDirectory: true, size: 0 },
        { name: 'Videos', isDirectory: true, size: 0 },
        { name: 'example.txt', isDirectory: false, size: 1024 },
        { name: 'photo.jpg', isDirectory: false, size: 2048576 },
        { name: 'document.pdf', isDirectory: false, size: 512000 }
      ]

      return mockFiles
    },
    
    // 处理文件点击
    handleFileClick(item: {name: string, isDirectory: boolean, size: number}) {
      if (item.isDirectory) {
        // 进入文件夹
        const newPath = this.currentPath + '/' + item.name
        this.loadFileList(newPath)
      } else {
        // 选择文件
        this.selectedFile = item
        this.showConfirmDialog = true
      }
    },
    
    // 获取文件图标
    getFileIcon(item: {name: string, isDirectory: boolean, size: number}) {
      if (item.isDirectory) {
        return '/static/images/folder-icon.png'
      } else {
        const ext = this.getFileExtension(item.name)
        switch (ext) {
          case 'jpg':
          case 'jpeg':
          case 'png':
          case 'gif':
            return '/static/images/image-icon.png'
          case 'mp4':
          case 'avi':
          case 'mov':
            return '/static/images/video-icon.png'
          case 'mp3':
          case 'wav':
          case 'flac':
            return '/static/images/audio-icon.png'
          case 'pdf':
            return '/static/images/pdf-icon.png'
          case 'doc':
          case 'docx':
            return '/static/images/doc-icon.png'
          default:
            return '/static/images/file-icon.png'
        }
      }
    },
    
    // 获取文件扩展名
    getFileExtension(filename: string) {
      return filename.split('.').pop()?.toLowerCase() || ''
    },
    
    // 格式化文件大小
    formatFileSize,
    
    // 回到根目录
    goToRoot() {
      this.loadFileList('/storage/emulated/0')
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 上一级目录
    goUp() {
      const pathParts = this.currentPath.split('/')
      if (pathParts.length > 2) {
        pathParts.pop()
        const parentPath = pathParts.join('/')
        this.loadFileList(parentPath)
      }
    },
    
    // 隐藏确认对话框
    hideConfirmDialog() {
      this.showConfirmDialog = false
      this.selectedFile = {}
    },
    
    // 确认上传
    async confirmUpload() {
      this.hideConfirmDialog()
      
      try {
        showToast('开始上传文件...')
        
        // 构建完整文件路径
        const fullPath = this.currentPath + '/' + this.selectedFile.name
        
        // 上传文件
        const result = await uploadFile(fullPath)
        
        if (result.status === 1) {
          // 保存上传记录
          saveUploadRecord({
            filename: this.selectedFile.name,
            gcode: result.get_code,
            url: `https://file.8845.top/file/${result.get_code}/${this.selectedFile.name}`
          })
          
          showToast('上传成功')
          
          // 返回主页并显示结果
          uni.navigateBack({
            success: () => {
              // 通过事件通知主页显示上传结果
              uni.$emit('uploadSuccess', {
                msg: result.msg,
                gcode: result.get_code,
                filename: this.selectedFile.name
              })
            }
          })
        } else {
          showToast(result.msg || '上传失败')
        }
      } catch (error) {
        console.error('上传失败:', error, " at pages/fileManager/fileManager.uvue:242")
        showToast('上传失败')
      }
    },
    
    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
})

export default __sfc__
function GenPagesFileManagerFileManagerRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
  return createElementVNode("view", utsMapOf({ class: "container" }), [
    createElementVNode("view", utsMapOf({ class: "path-bar" }), [
      createElementVNode("text", utsMapOf({ class: "path-text" }), toDisplayString(_ctx.currentPath), 1 /* TEXT */)
    ]),
    createElementVNode("scroll-view", utsMapOf({
      class: "file-list",
      "scroll-y": "true"
    }), [
      createElementVNode(Fragment, null, RenderHelpers.renderList(_ctx.fileList, (item, index, __index, _cached): any => {
        return createElementVNode("view", utsMapOf({
          class: "file-item",
          key: index,
          onClick: () => {_ctx.handleFileClick(item)}
        }), [
          createElementVNode("image", utsMapOf({
            class: "file-icon",
            src: _ctx.getFileIcon(item),
            mode: "aspectFit"
          }), null, 8 /* PROPS */, ["src"]),
          createElementVNode("view", utsMapOf({ class: "file-info" }), [
            createElementVNode("text", utsMapOf({ class: "file-name" }), toDisplayString(item.name), 1 /* TEXT */),
            isTrue(!item.isDirectory)
              ? createElementVNode("text", utsMapOf({
                  key: 0,
                  class: "file-detail"
                }), toDisplayString(_ctx.formatFileSize(item.size)), 1 /* TEXT */)
              : createCommentVNode("v-if", true)
          ]),
          isTrue(item.isDirectory)
            ? createElementVNode("view", utsMapOf({
                key: 0,
                class: "file-arrow"
              }), [
                createElementVNode("text", utsMapOf({ class: "arrow-text" }), ">")
              ])
            : createCommentVNode("v-if", true)
        ], 8 /* PROPS */, ["onClick"])
      }), 128 /* KEYED_FRAGMENT */),
      _ctx.fileList.length === 0
        ? createElementVNode("view", utsMapOf({
            key: 0,
            class: "empty-state"
          }), [
            createElementVNode("text", utsMapOf({ class: "empty-text" }), "此文件夹为空")
          ])
        : createCommentVNode("v-if", true)
    ]),
    createElementVNode("view", utsMapOf({ class: "bottom-bar" }), [
      createElementVNode("view", utsMapOf({
        class: "bar-item",
        onClick: _ctx.goToRoot
      }), [
        createElementVNode("text", utsMapOf({ class: "bar-text" }), "根目录")
      ], 8 /* PROPS */, ["onClick"]),
      createElementVNode("view", utsMapOf({
        class: "bar-item",
        onClick: _ctx.goBack
      }), [
        createElementVNode("text", utsMapOf({ class: "bar-text" }), "返回")
      ], 8 /* PROPS */, ["onClick"]),
      createElementVNode("view", utsMapOf({
        class: "bar-item",
        onClick: _ctx.goUp
      }), [
        createElementVNode("text", utsMapOf({ class: "bar-text" }), "上一级")
      ], 8 /* PROPS */, ["onClick"])
    ]),
    isTrue(_ctx.showConfirmDialog)
      ? createElementVNode("view", utsMapOf({
          key: 0,
          class: "modal-overlay",
          onClick: _ctx.hideConfirmDialog
        }), [
          createElementVNode("view", utsMapOf({
            class: "modal-content",
            onClick: withModifiers(() => {}, ["stop"])
          }), [
            createElementVNode("view", utsMapOf({ class: "modal-header" }), [
              createElementVNode("text", utsMapOf({ class: "modal-title" }), toDisplayString(_ctx.selectedFile.name), 1 /* TEXT */)
            ]),
            createElementVNode("view", utsMapOf({ class: "modal-body" }), [
              createElementVNode("text", utsMapOf({ class: "modal-text" }), "是否上传该文件")
            ]),
            createElementVNode("view", utsMapOf({ class: "modal-actions" }), [
              createElementVNode("button", utsMapOf({
                class: "modal-btn cancel",
                onClick: _ctx.hideConfirmDialog
              }), "取消", 8 /* PROPS */, ["onClick"]),
              createElementVNode("button", utsMapOf({
                class: "modal-btn confirm",
                onClick: _ctx.confirmUpload
              }), "确定", 8 /* PROPS */, ["onClick"])
            ])
          ], 8 /* PROPS */, ["onClick"])
        ], 8 /* PROPS */, ["onClick"])
      : createCommentVNode("v-if", true),
    isTrue(_ctx.loading)
      ? createElementVNode("view", utsMapOf({
          key: 1,
          class: "loading-overlay"
        }), [
          createElementVNode("view", utsMapOf({ class: "loading-content" }), [
            createElementVNode("view", utsMapOf({ class: "loading-spinner" })),
            createElementVNode("text", utsMapOf({ class: "loading-text" }), "加载中...")
          ])
        ])
      : createCommentVNode("v-if", true)
  ])
}
const GenPagesFileManagerFileManagerStyles = [utsMapOf([["container", padStyleMapOf(utsMapOf([["display", "flex"], ["flexDirection", "column"], ["backgroundColor", "#ffffff"]]))], ["path-bar", padStyleMapOf(utsMapOf([["backgroundColor", "#1f999b"], ["paddingTop", 15], ["paddingRight", 20], ["paddingBottom", 15], ["paddingLeft", 20], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#e0e0e0"]]))], ["path-text", padStyleMapOf(utsMapOf([["color", "#ffffff"], ["fontSize", 14]]))], ["file-list", padStyleMapOf(utsMapOf([["flex", 1], ["paddingTop", 10], ["paddingRight", 0], ["paddingBottom", 10], ["paddingLeft", 0]]))], ["file-item", padStyleMapOf(utsMapOf([["display", "flex"], ["alignItems", "center"], ["paddingTop", 15], ["paddingRight", 20], ["paddingBottom", 15], ["paddingLeft", 20], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#f0f0f0"], ["transitionProperty", "backgroundColor"], ["transitionDuration", "0.2s"], ["transitionTimingFunction", "ease"], ["backgroundColor:active", "#f8f8f8"]]))], ["file-icon", padStyleMapOf(utsMapOf([["width", 40], ["height", 40], ["marginRight", 15]]))], ["file-info", padStyleMapOf(utsMapOf([["flex", 1], ["display", "flex"], ["flexDirection", "column"]]))], ["file-name", padStyleMapOf(utsMapOf([["fontSize", 16], ["color", "#333333"], ["marginBottom", 2]]))], ["file-detail", padStyleMapOf(utsMapOf([["fontSize", 12], ["color", "#999999"]]))], ["file-arrow", padStyleMapOf(utsMapOf([["marginLeft", 10]]))], ["arrow-text", padStyleMapOf(utsMapOf([["fontSize", 18], ["color", "#cccccc"], ["fontWeight", "bold"]]))], ["empty-state", padStyleMapOf(utsMapOf([["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["height", 200]]))], ["empty-text", padStyleMapOf(utsMapOf([["fontSize", 16], ["color", "#999999"]]))], ["bottom-bar", padStyleMapOf(utsMapOf([["display", "flex"], ["backgroundColor", "#ffffff"], ["borderTopWidth", 1], ["borderTopStyle", "solid"], ["borderTopColor", "#e0e0e0"], ["paddingTop", 10], ["paddingRight", 0], ["paddingBottom", 10], ["paddingLeft", 0]]))], ["bar-item", padStyleMapOf(utsMapOf([["flex", 1], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["paddingTop", 10], ["paddingRight", 10], ["paddingBottom", 10], ["paddingLeft", 10], ["cursor", "pointer"], ["backgroundColor:active", "#f0f0f0"]]))], ["bar-text", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#1f999b"], ["fontWeight", "bold"]]))], ["modal-overlay", padStyleMapOf(utsMapOf([["position", "fixed"], ["top", 0], ["left", 0], ["right", 0], ["bottom", 0], ["backgroundColor", "rgba(0,0,0,0.5)"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["zIndex", 1000]]))], ["modal-content", padStyleMapOf(utsMapOf([["backgroundColor", "#ffffff"], ["borderRadius", 10], ["marginTop", 20], ["marginRight", 20], ["marginBottom", 20], ["marginLeft", 20], ["maxWidth", 300], ["width", "90%"], ["overflow", "hidden"]]))], ["modal-header", padStyleMapOf(utsMapOf([["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#f0f0f0"]]))], ["modal-title", padStyleMapOf(utsMapOf([["fontSize", 17], ["fontWeight", "bold"], ["color", "#1f999b"], ["textAlign", "center"]]))], ["modal-body", padStyleMapOf(utsMapOf([["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20], ["textAlign", "center"]]))], ["modal-text", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#666666"]]))], ["modal-actions", padStyleMapOf(utsMapOf([["display", "flex"], ["borderTopWidth", 1], ["borderTopStyle", "solid"], ["borderTopColor", "#f0f0f0"]]))], ["modal-btn", utsMapOf([["", utsMapOf([["flex", 1], ["paddingTop", 15], ["paddingRight", 15], ["paddingBottom", 15], ["paddingLeft", 15], ["borderWidth", "medium"], ["borderStyle", "none"], ["borderColor", "#000000"], ["backgroundColor", "#ffffff"], ["fontSize", 16], ["fontWeight", "bold"], ["cursor", "pointer"], ["backgroundColor:active", "#f8f8f8"]])], [".cancel", utsMapOf([["color", "#999999"], ["borderRightWidth", 1], ["borderRightStyle", "solid"], ["borderRightColor", "#f0f0f0"]])], [".confirm", utsMapOf([["color", "#1f999b"]])]])], ["loading-overlay", padStyleMapOf(utsMapOf([["position", "fixed"], ["top", 0], ["left", 0], ["right", 0], ["bottom", 0], ["backgroundColor", "rgba(0,0,0,0.3)"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["zIndex", 999]]))], ["loading-content", padStyleMapOf(utsMapOf([["backgroundColor", "#ffffff"], ["borderRadius", 10], ["paddingTop", 30], ["paddingRight", 30], ["paddingBottom", 30], ["paddingLeft", 30], ["display", "flex"], ["flexDirection", "column"], ["alignItems", "center"]]))], ["loading-spinner", padStyleMapOf(utsMapOf([["width", 30], ["height", 30], ["borderWidth", 3], ["borderStyle", "solid"], ["borderColor", "#f3f3f3"], ["borderTopWidth", 3], ["borderTopStyle", "solid"], ["borderTopColor", "#1f999b"], ["animation", "spin 1s linear infinite"], ["marginBottom", 15]]))], ["loading-text", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#1f999b"], ["fontWeight", "bold"]]))], ["@FONT-FACE", utsMapOf([["0", utsMapOf([])]])], ["@TRANSITION", utsMapOf([["file-item", utsMapOf([["property", "backgroundColor"], ["duration", "0.2s"], ["timingFunction", "ease"]])]])]])]

//# sourceMappingURL=fileManager.uvue.map