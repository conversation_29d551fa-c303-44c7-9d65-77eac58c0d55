<template>
  <view class="dialog-overlay" v-if="visible" @click="handleOverlayClick">
    <view class="dialog-content" @click.stop>
      <view class="dialog-header" v-if="title">
        <text class="dialog-title">{{ title }}</text>
      </view>
      <view class="dialog-body">
        <text class="dialog-message">{{ message }}</text>
      </view>
      <view class="dialog-actions">
        <button class="dialog-btn cancel" @click="handleCancel">{{ cancelText }}</button>
        <button class="dialog-btn confirm" @click="handleConfirm">{{ confirmText }}</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ConfirmDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    message: {
      type: String,
      default: '确定要执行此操作吗？'
    },
    confirmText: {
      type: String,
      default: '确定'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    maskClosable: {
      type: Boolean,
      default: true
    }
  },
  
  methods: {
    handleOverlayClick() {
      if (this.maskClosable) {
        this.handleCancel()
      }
    },
    
    handleConfirm() {
      this.$emit('confirm')
      this.$emit('update:visible', false)
    },
    
    handleCancel() {
      this.$emit('cancel')
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.dialog-content {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 20px;
  max-width: 320px;
  width: 90%;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.dialog-header {
  padding: 20px 20px 10px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.dialog-body {
  padding: 20px;
  text-align: center;
}

.dialog-message {
  font-size: 16px;
  color: #666666;
  line-height: 1.5;
}

.dialog-actions {
  display: flex;
  border-top: 1px solid #f0f0f0;
}

.dialog-btn {
  flex: 1;
  padding: 15px;
  border: none;
  background-color: #ffffff;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dialog-btn.cancel {
  color: #999999;
  border-right: 1px solid #f0f0f0;
}

.dialog-btn.confirm {
  color: #1f999b;
}

.dialog-btn:active {
  background-color: #f8f8f8;
}
</style>
