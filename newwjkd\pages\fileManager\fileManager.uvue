<template>
  <view class="container">
    <!-- 顶部路径显示 -->
    <view class="path-bar">
      <text class="path-text">{{ currentPath }}</text>
    </view>
    
    <!-- 文件列表 -->
    <scroll-view class="file-list" scroll-y="true">
      <view class="file-item" v-for="(item, index) in fileList" :key="index" @click="handleFileClick(item)">
        <image class="file-icon" :src="getFileIcon(item)" mode="aspectFit"></image>
        <view class="file-info">
          <text class="file-name">{{ item.name }}</text>
          <text class="file-detail" v-if="!item.isDirectory">{{ formatFileSize(item.size) }}</text>
        </view>
        <view class="file-arrow" v-if="item.isDirectory">
          <text class="arrow-text">></text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="fileList.length === 0">
        <text class="empty-text">此文件夹为空</text>
      </view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="bar-item" @click="goToRoot">
        <text class="bar-text">根目录</text>
      </view>
      <view class="bar-item" @click="goBack">
        <text class="bar-text">返回</text>
      </view>
      <view class="bar-item" @click="goUp">
        <text class="bar-text">上一级</text>
      </view>
    </view>
    
    <!-- 文件确认对话框 -->
    <view class="modal-overlay" v-if="showConfirmDialog" @click="hideConfirmDialog">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{ selectedFile.name }}</text>
        </view>
        <view class="modal-body">
          <text class="modal-text">是否上传该文件</text>
        </view>
        <view class="modal-actions">
          <button class="modal-btn cancel" @click="hideConfirmDialog">取消</button>
          <button class="modal-btn confirm" @click="confirmUpload">确定</button>
        </view>
      </view>
    </view>
    
    <!-- 加载中 -->
    <view class="loading-overlay" v-if="loading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>
  </view>
</template>

<script>
import { uploadFile } from '@/utils/api.js'
import { saveUploadRecord } from '@/utils/storage.js'
import { showToast, formatFileSize } from '@/utils/common.js'

export default {
  data() {
    return {
      currentPath: '/storage/emulated/0',
      fileList: [] as Array<{name: string, isDirectory: boolean, size: number}>,
      loading: false,
      showConfirmDialog: false,
      selectedFile: {
        name: '',
        isDirectory: false,
        size: 0
      } as {name: string, isDirectory: boolean, size: number}
    }
  },
  
  onLoad() {
    this.loadFileList(this.currentPath)
  },
  
  methods: {
    // 加载文件列表
    async loadFileList(path) {
      this.loading = true
      try {
        // 在实际应用中，这里需要调用原生API获取文件列表
        // 由于uni-app x的限制，这里使用模拟数据
        await this.delay(500)
        
        this.fileList = await this.getFileListFromPath(path)
        this.currentPath = path
      } catch (error) {
        console.error('加载文件列表失败:', error)
        showToast('加载文件列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 模拟获取文件列表（实际项目中需要调用原生API）
    async getFileListFromPath(path: string): Promise<Array<{name: string, isDirectory: boolean, size: number}>> {
      // 这里返回模拟数据，实际项目中需要调用原生文件系统API
      const mockFiles: Array<{name: string, isDirectory: boolean, size: number}> = [
        { name: 'Documents', isDirectory: true, size: 0 },
        { name: 'Download', isDirectory: true, size: 0 },
        { name: 'Pictures', isDirectory: true, size: 0 },
        { name: 'Music', isDirectory: true, size: 0 },
        { name: 'Videos', isDirectory: true, size: 0 },
        { name: 'example.txt', isDirectory: false, size: 1024 },
        { name: 'photo.jpg', isDirectory: false, size: 2048576 },
        { name: 'document.pdf', isDirectory: false, size: 512000 }
      ]

      return mockFiles
    },
    
    // 处理文件点击
    handleFileClick(item: {name: string, isDirectory: boolean, size: number}) {
      if (item.isDirectory) {
        // 进入文件夹
        const newPath = this.currentPath + '/' + item.name
        this.loadFileList(newPath)
      } else {
        // 选择文件
        this.selectedFile = item
        this.showConfirmDialog = true
      }
    },
    
    // 获取文件图标
    getFileIcon(item: {name: string, isDirectory: boolean, size: number}) {
      if (item.isDirectory) {
        return '/static/images/folder-icon.png'
      } else {
        const ext = this.getFileExtension(item.name)
        switch (ext) {
          case 'jpg':
          case 'jpeg':
          case 'png':
          case 'gif':
            return '/static/images/image-icon.png'
          case 'mp4':
          case 'avi':
          case 'mov':
            return '/static/images/video-icon.png'
          case 'mp3':
          case 'wav':
          case 'flac':
            return '/static/images/audio-icon.png'
          case 'pdf':
            return '/static/images/pdf-icon.png'
          case 'doc':
          case 'docx':
            return '/static/images/doc-icon.png'
          default:
            return '/static/images/file-icon.png'
        }
      }
    },
    
    // 获取文件扩展名
    getFileExtension(filename: string) {
      return filename.split('.').pop()?.toLowerCase() || ''
    },
    
    // 格式化文件大小
    formatFileSize,
    
    // 回到根目录
    goToRoot() {
      this.loadFileList('/storage/emulated/0')
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 上一级目录
    goUp() {
      const pathParts = this.currentPath.split('/')
      if (pathParts.length > 2) {
        pathParts.pop()
        const parentPath = pathParts.join('/')
        this.loadFileList(parentPath)
      }
    },
    
    // 隐藏确认对话框
    hideConfirmDialog() {
      this.showConfirmDialog = false
      this.selectedFile = {}
    },
    
    // 确认上传
    async confirmUpload() {
      this.hideConfirmDialog()
      
      try {
        showToast('开始上传文件...')
        
        // 构建完整文件路径
        const fullPath = this.currentPath + '/' + this.selectedFile.name
        
        // 上传文件
        const result = await uploadFile(fullPath)
        
        if (result.status === 1) {
          // 保存上传记录
          saveUploadRecord({
            filename: this.selectedFile.name,
            gcode: result.get_code,
            url: `https://file.8845.top/file/${result.get_code}/${this.selectedFile.name}`
          })
          
          showToast('上传成功')
          
          // 返回主页并显示结果
          uni.navigateBack({
            success: () => {
              // 通过事件通知主页显示上传结果
              uni.$emit('uploadSuccess', {
                msg: result.msg,
                gcode: result.get_code,
                filename: this.selectedFile.name
              })
            }
          })
        } else {
          showToast(result.msg || '上传失败')
        }
      } catch (error) {
        console.error('上传失败:', error)
        showToast('上传失败')
      }
    },
    
    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style scoped>
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

/* 路径栏 */
.path-bar {
  background-color: #1f999b;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.path-text {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

/* 文件列表 */
.file-list {
  flex: 1;
  padding: 10px 0;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.file-item:active {
  background-color: #f8f8f8;
}

.file-icon {
  width: 40px;
  height: 40px;
  margin-right: 15px;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.file-name {
  font-size: 16px;
  color: #333333;
  font-weight: 500;
  margin-bottom: 2px;
}

.file-detail {
  font-size: 12px;
  color: #999999;
}

.file-arrow {
  margin-left: 10px;
}

.arrow-text {
  font-size: 18px;
  color: #cccccc;
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-text {
  font-size: 16px;
  color: #999999;
}

/* 底部操作栏 */
.bottom-bar {
  display: flex;
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
  padding: 10px 0;
}

.bar-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  cursor: pointer;
}

.bar-item:active {
  background-color: #f0f0f0;
}

.bar-text {
  font-size: 14px;
  color: #1f999b;
  font-weight: bold;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #ffffff;
  border-radius: 10px;
  margin: 20px;
  max-width: 300px;
  width: 90%;
  overflow: hidden;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 17px;
  font-weight: bold;
  color: #1f999b;
  text-align: center;
}

.modal-body {
  padding: 20px;
  text-align: center;
}

.modal-text {
  font-size: 14px;
  color: #666666;
}

.modal-actions {
  display: flex;
  border-top: 1px solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 15px;
  border: none;
  background-color: #ffffff;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
}

.modal-btn.cancel {
  color: #999999;
  border-right: 1px solid #f0f0f0;
}

.modal-btn.confirm {
  color: #1f999b;
}

.modal-btn:active {
  background-color: #f8f8f8;
}

/* 加载中样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-content {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1f999b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #1f999b;
  font-weight: bold;
}
</style>
