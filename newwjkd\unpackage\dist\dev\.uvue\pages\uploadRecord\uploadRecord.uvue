
import { getUploadRecords, deleteUploadRecord, clearUploadRecords } from '@/utils/storage.js'
import { showToast, showConfirm, copyToClipboard } from '@/utils/common.js'

const __sfc__ = defineComponent({
  data() {
    return {
      records: [],
      showClearDialog: false
    }
  },
  
  onLoad() {
    this.loadRecords()
  },
  
  onShow() {
    // 每次显示页面时重新加载记录
    this.loadRecords()
  },
  
  methods: {
    // 加载记录
    loadRecords() {
      this.records = getUploadRecords()
    },
    
    // 复制取件码
    async copyCode(code) {
      await copyToClipboard(code)
    },
    
    // 复制URL
    async copyUrl(url) {
      await copyToClipboard(url)
    },
    
    // 删除记录
    async deleteRecord(id, index) {
      const confirmed = await showConfirm('确定要删除这条记录吗？')
      if (confirmed) {
        const success = deleteUploadRecord(id)
        if (success) {
          this.records.splice(index, 1)
          showToast('删除成功')
        } else {
          showToast('删除失败')
        }
      }
    },
    
    // 显示清空确认对话框
    showClearConfirm() {
      this.showClearDialog = true
    },
    
    // 隐藏清空确认对话框
    hideClearDialog() {
      this.showClearDialog = false
    },
    
    // 清空所有记录
    clearAllRecords() {
      const success = clearUploadRecords()
      if (success) {
        this.records = []
        this.showClearDialog = false
        showToast('清空成功')
      } else {
        showToast('清空失败')
      }
    }
  }
})

export default __sfc__
function GenPagesUploadRecordUploadRecordRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
  return createElementVNode("view", utsMapOf({ class: "container" }), [
    createElementVNode("view", utsMapOf({ class: "header" }), [
      createElementVNode("text", utsMapOf({ class: "header-title" }), "上传记录"),
      createElementVNode("text", utsMapOf({ class: "header-subtitle" }), "共 " + toDisplayString(_ctx.records.length) + " 条记录", 1 /* TEXT */)
    ]),
    _ctx.records.length > 0
      ? createElementVNode("scroll-view", utsMapOf({
          key: 0,
          class: "record-list",
          "scroll-y": "true"
        }), [
          createElementVNode(Fragment, null, RenderHelpers.renderList(_ctx.records, (record, index, __index, _cached): any => {
            return createElementVNode("view", utsMapOf({
              class: "record-item",
              key: record.id
            }), [
              createElementVNode("view", utsMapOf({ class: "record-header" }), [
                createElementVNode("text", utsMapOf({ class: "file-name" }), toDisplayString(record.filename), 1 /* TEXT */),
                createElementVNode("text", utsMapOf({ class: "upload-time" }), toDisplayString(record.uploadTime), 1 /* TEXT */)
              ]),
              createElementVNode("view", utsMapOf({ class: "record-body" }), [
                createElementVNode("view", utsMapOf({ class: "record-row" }), [
                  createElementVNode("text", utsMapOf({ class: "label" }), "取件码："),
                  createElementVNode("text", utsMapOf({
                    class: "value code",
                    onClick: () => {_ctx.copyCode(record.gcode)}
                  }), toDisplayString(record.gcode), 9 /* TEXT, PROPS */, ["onClick"])
                ]),
                isTrue(record.url)
                  ? createElementVNode("view", utsMapOf({
                      key: 0,
                      class: "record-row"
                    }), [
                      createElementVNode("text", utsMapOf({ class: "label" }), "文件地址："),
                      createElementVNode("text", utsMapOf({
                        class: "value url",
                        onClick: () => {_ctx.copyUrl(record.url)}
                      }), toDisplayString(record.url), 9 /* TEXT, PROPS */, ["onClick"])
                    ])
                  : createCommentVNode("v-if", true)
              ]),
              createElementVNode("view", utsMapOf({ class: "record-actions" }), [
                createElementVNode("button", utsMapOf({
                  class: "action-btn copy",
                  onClick: () => {_ctx.copyCode(record.gcode)}
                }), "复制取件码", 8 /* PROPS */, ["onClick"]),
                createElementVNode("button", utsMapOf({
                  class: "action-btn delete",
                  onClick: () => {_ctx.deleteRecord(record.id, index)}
                }), "删除", 8 /* PROPS */, ["onClick"])
              ])
            ])
          }), 128 /* KEYED_FRAGMENT */)
        ])
      : createElementVNode("view", utsMapOf({
          key: 1,
          class: "empty-state"
        }), [
          createElementVNode("image", utsMapOf({
            class: "empty-icon",
            src: "/static/images/empty-record.png",
            mode: "aspectFit"
          })),
          createElementVNode("text", utsMapOf({ class: "empty-text" }), "暂无上传记录"),
          createElementVNode("text", utsMapOf({ class: "empty-desc" }), "上传文件后会在这里显示记录")
        ]),
    _ctx.records.length > 0
      ? createElementVNode("view", utsMapOf({
          key: 2,
          class: "bottom-bar"
        }), [
          createElementVNode("button", utsMapOf({
            class: "bottom-btn clear",
            onClick: _ctx.showClearConfirm
          }), "清空记录", 8 /* PROPS */, ["onClick"])
        ])
      : createCommentVNode("v-if", true),
    isTrue(_ctx.showClearDialog)
      ? createElementVNode("view", utsMapOf({
          key: 3,
          class: "modal-overlay",
          onClick: _ctx.hideClearDialog
        }), [
          createElementVNode("view", utsMapOf({
            class: "modal-content",
            onClick: withModifiers(() => {}, ["stop"])
          }), [
            createElementVNode("view", utsMapOf({ class: "modal-header" }), [
              createElementVNode("text", utsMapOf({ class: "modal-title" }), "清空记录")
            ]),
            createElementVNode("view", utsMapOf({ class: "modal-body" }), [
              createElementVNode("text", utsMapOf({ class: "modal-text" }), "确定要清空所有上传记录吗？此操作不可恢复。")
            ]),
            createElementVNode("view", utsMapOf({ class: "modal-actions" }), [
              createElementVNode("button", utsMapOf({
                class: "modal-btn cancel",
                onClick: _ctx.hideClearDialog
              }), "取消", 8 /* PROPS */, ["onClick"]),
              createElementVNode("button", utsMapOf({
                class: "modal-btn confirm",
                onClick: _ctx.clearAllRecords
              }), "确定", 8 /* PROPS */, ["onClick"])
            ])
          ], 8 /* PROPS */, ["onClick"])
        ], 8 /* PROPS */, ["onClick"])
      : createCommentVNode("v-if", true)
  ])
}
const GenPagesUploadRecordUploadRecordStyles = [utsMapOf([["container", padStyleMapOf(utsMapOf([["display", "flex"], ["flexDirection", "column"], ["backgroundColor", "#f8f8f8"]]))], ["header", padStyleMapOf(utsMapOf([["backgroundImage", "linear-gradient(135deg, #1f999b 0%, #16a085 100%)"], ["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20], ["color", "#ffffff"]]))], ["header-title", padStyleMapOf(utsMapOf([["fontSize", 20], ["fontWeight", "bold"], ["marginBottom", 5]]))], ["header-subtitle", padStyleMapOf(utsMapOf([["fontSize", 14], ["opacity", 0.9]]))], ["record-list", padStyleMapOf(utsMapOf([["flex", 1], ["paddingTop", 15], ["paddingRight", 15], ["paddingBottom", 15], ["paddingLeft", 15]]))], ["record-item", padStyleMapOf(utsMapOf([["backgroundColor", "#ffffff"], ["borderRadius", 12], ["marginBottom", 15], ["paddingTop", 15], ["paddingRight", 15], ["paddingBottom", 15], ["paddingLeft", 15], ["boxShadow", "0 2px 8px rgba(0, 0, 0, 0.1)"]]))], ["record-header", padStyleMapOf(utsMapOf([["display", "flex"], ["justifyContent", "space-between"], ["alignItems", "flex-start"], ["marginBottom", 12], ["paddingBottom", 8], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#f0f0f0"]]))], ["file-name", padStyleMapOf(utsMapOf([["fontSize", 16], ["fontWeight", "bold"], ["color", "#333333"], ["flex", 1], ["marginRight", 10]]))], ["upload-time", padStyleMapOf(utsMapOf([["fontSize", 12], ["color", "#999999"]]))], ["record-body", padStyleMapOf(utsMapOf([["marginBottom", 12]]))], ["record-row", padStyleMapOf(utsMapOf([["display", "flex"], ["marginBottom", 8], ["alignItems", "flex-start"]]))], ["label", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#666666"], ["width", 80], ["flexShrink", 0]]))], ["value", utsMapOf([["", utsMapOf([["fontSize", 14], ["flex", 1], ["wordBreak", "break-all"]])], [".code", utsMapOf([["color", "#f98b8b"], ["fontWeight", "bold"], ["cursor", "pointer"]])], [".url", utsMapOf([["color", "#1f999b"], ["cursor", "pointer"]])]])], ["record-actions", padStyleMapOf(utsMapOf([["display", "flex"], ["gap", "10px"]]))], ["action-btn", utsMapOf([["", utsMapOf([["flex", 1], ["paddingTop", 8], ["paddingRight", 12], ["paddingBottom", 8], ["paddingLeft", 12], ["borderRadius", 6], ["fontSize", 12], ["fontWeight", "bold"], ["borderWidth", "medium"], ["borderStyle", "none"], ["borderColor", "#000000"], ["cursor", "pointer"]])], [".copy", utsMapOf([["backgroundColor", "#1f999b"], ["color", "#ffffff"]])], [".delete", utsMapOf([["backgroundColor", "#ff6b6b"], ["color", "#ffffff"]])]])], ["empty-state", padStyleMapOf(utsMapOf([["flex", 1], ["display", "flex"], ["flexDirection", "column"], ["justifyContent", "center"], ["alignItems", "center"], ["paddingTop", 40], ["paddingRight", 20], ["paddingBottom", 40], ["paddingLeft", 20]]))], ["empty-icon", padStyleMapOf(utsMapOf([["width", 120], ["height", 120], ["marginBottom", 20], ["opacity", 0.6]]))], ["empty-text", padStyleMapOf(utsMapOf([["fontSize", 18], ["color", "#999999"], ["fontWeight", "bold"], ["marginBottom", 8]]))], ["empty-desc", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#cccccc"], ["textAlign", "center"]]))], ["bottom-bar", padStyleMapOf(utsMapOf([["paddingTop", 15], ["paddingRight", 20], ["paddingBottom", 15], ["paddingLeft", 20], ["backgroundColor", "#ffffff"], ["borderTopWidth", 1], ["borderTopStyle", "solid"], ["borderTopColor", "#e0e0e0"]]))], ["bottom-btn", utsMapOf([["", utsMapOf([["width", "100%"], ["paddingTop", 12], ["paddingRight", 12], ["paddingBottom", 12], ["paddingLeft", 12], ["borderRadius", 8], ["fontSize", 16], ["fontWeight", "bold"], ["borderWidth", "medium"], ["borderStyle", "none"], ["borderColor", "#000000"], ["cursor", "pointer"]])], [".clear", utsMapOf([["backgroundColor", "#ff6b6b"], ["color", "#ffffff"]])]])], ["modal-overlay", padStyleMapOf(utsMapOf([["position", "fixed"], ["top", 0], ["left", 0], ["right", 0], ["bottom", 0], ["backgroundColor", "rgba(0,0,0,0.5)"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["zIndex", 1000]]))], ["modal-content", padStyleMapOf(utsMapOf([["backgroundColor", "#ffffff"], ["borderRadius", 12], ["marginTop", 20], ["marginRight", 20], ["marginBottom", 20], ["marginLeft", 20], ["maxWidth", 300], ["width", "90%"], ["overflow", "hidden"]]))], ["modal-header", padStyleMapOf(utsMapOf([["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 10], ["paddingLeft", 20], ["textAlign", "center"]]))], ["modal-title", padStyleMapOf(utsMapOf([["fontSize", 18], ["fontWeight", "bold"], ["color", "#333333"]]))], ["modal-body", padStyleMapOf(utsMapOf([["paddingTop", 10], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20], ["textAlign", "center"]]))], ["modal-text", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#666666"], ["lineHeight", 1.5]]))], ["modal-actions", padStyleMapOf(utsMapOf([["display", "flex"], ["borderTopWidth", 1], ["borderTopStyle", "solid"], ["borderTopColor", "#f0f0f0"]]))], ["modal-btn", utsMapOf([["", utsMapOf([["flex", 1], ["paddingTop", 15], ["paddingRight", 15], ["paddingBottom", 15], ["paddingLeft", 15], ["borderWidth", "medium"], ["borderStyle", "none"], ["borderColor", "#000000"], ["backgroundColor", "#ffffff"], ["fontSize", 16], ["fontWeight", "bold"], ["cursor", "pointer"], ["backgroundColor:active", "#f8f8f8"]])], [".cancel", utsMapOf([["color", "#999999"], ["borderRightWidth", 1], ["borderRightStyle", "solid"], ["borderRightColor", "#f0f0f0"]])], [".confirm", utsMapOf([["color", "#ff6b6b"]])]])]])]

//# sourceMappingURL=uploadRecord.uvue.map