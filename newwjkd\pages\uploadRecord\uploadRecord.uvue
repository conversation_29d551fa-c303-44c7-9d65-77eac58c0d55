<template>
  <view class="container">
    <!-- 头部 -->
    <view class="header">
      <text class="header-title">上传记录</text>
      <text class="header-subtitle">共 {{ records.length }} 条记录</text>
    </view>
    
    <!-- 记录列表 -->
    <scroll-view class="record-list" scroll-y="true" v-if="records.length > 0">
      <view class="record-item" v-for="(record, index) in records" :key="record.id">
        <view class="record-header">
          <text class="file-name">{{ record.filename }}</text>
          <text class="upload-time">{{ record.uploadTime }}</text>
        </view>
        <view class="record-body">
          <view class="record-row">
            <text class="label">取件码：</text>
            <text class="value code" @click="copyCode(record.gcode)">{{ record.gcode }}</text>
          </view>
          <view class="record-row" v-if="record.url">
            <text class="label">文件地址：</text>
            <text class="value url" @click="copyUrl(record.url)">{{ record.url }}</text>
          </view>
        </view>
        <view class="record-actions">
          <button class="action-btn copy" @click="copyCode(record.gcode)">复制取件码</button>
          <button class="action-btn delete" @click="deleteRecord(record.id, index)">删除</button>
        </view>
      </view>
    </scroll-view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image class="empty-icon" src="/static/images/empty-record.png" mode="aspectFit"></image>
      <text class="empty-text">暂无上传记录</text>
      <text class="empty-desc">上传文件后会在这里显示记录</text>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar" v-if="records.length > 0">
      <button class="bottom-btn clear" @click="showClearConfirm">清空记录</button>
    </view>
    
    <!-- 清空确认对话框 -->
    <view class="modal-overlay" v-if="showClearDialog" @click="hideClearDialog">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">清空记录</text>
        </view>
        <view class="modal-body">
          <text class="modal-text">确定要清空所有上传记录吗？此操作不可恢复。</text>
        </view>
        <view class="modal-actions">
          <button class="modal-btn cancel" @click="hideClearDialog">取消</button>
          <button class="modal-btn confirm" @click="clearAllRecords">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getUploadRecords, deleteUploadRecord, clearUploadRecords } from '@/utils/storage.js'
import { showToast, showConfirm, copyToClipboard } from '@/utils/common.js'

export default {
  data() {
    return {
      records: [],
      showClearDialog: false
    }
  },
  
  onLoad() {
    this.loadRecords()
  },
  
  onShow() {
    // 每次显示页面时重新加载记录
    this.loadRecords()
  },
  
  methods: {
    // 加载记录
    loadRecords() {
      this.records = getUploadRecords()
    },
    
    // 复制取件码
    async copyCode(code) {
      await copyToClipboard(code)
    },
    
    // 复制URL
    async copyUrl(url) {
      await copyToClipboard(url)
    },
    
    // 删除记录
    async deleteRecord(id, index) {
      const confirmed = await showConfirm('确定要删除这条记录吗？')
      if (confirmed) {
        const success = deleteUploadRecord(id)
        if (success) {
          this.records.splice(index, 1)
          showToast('删除成功')
        } else {
          showToast('删除失败')
        }
      }
    },
    
    // 显示清空确认对话框
    showClearConfirm() {
      this.showClearDialog = true
    },
    
    // 隐藏清空确认对话框
    hideClearDialog() {
      this.showClearDialog = false
    },
    
    // 清空所有记录
    clearAllRecords() {
      const success = clearUploadRecords()
      if (success) {
        this.records = []
        this.showClearDialog = false
        showToast('清空成功')
      } else {
        showToast('清空失败')
      }
    }
  }
}
</script>

<style scoped>
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

/* 头部 */
.header {
  background: linear-gradient(135deg, #1f999b 0%, #16a085 100%);
  padding: 20px;
  color: #ffffff;
}

.header-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
}

.header-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

/* 记录列表 */
.record-list {
  flex: 1;
  padding: 15px;
}

.record-item {
  background-color: #ffffff;
  border-radius: 12px;
  margin-bottom: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.file-name {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  flex: 1;
  margin-right: 10px;
}

.upload-time {
  font-size: 12px;
  color: #999999;
}

.record-body {
  margin-bottom: 12px;
}

.record-row {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;
}

.label {
  font-size: 14px;
  color: #666666;
  width: 80px;
  flex-shrink: 0;
}

.value {
  font-size: 14px;
  flex: 1;
  word-break: break-all;
}

.value.code {
  color: #f98b8b;
  font-weight: bold;
  cursor: pointer;
}

.value.url {
  color: #1f999b;
  cursor: pointer;
}

.record-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  flex: 1;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: bold;
  border: none;
  cursor: pointer;
}

.action-btn.copy {
  background-color: #1f999b;
  color: #ffffff;
}

.action-btn.delete {
  background-color: #ff6b6b;
  color: #ffffff;
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

.empty-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-text {
  font-size: 18px;
  color: #999999;
  font-weight: bold;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #cccccc;
  text-align: center;
}

/* 底部操作栏 */
.bottom-bar {
  padding: 15px 20px;
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
}

.bottom-btn {
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  border: none;
  cursor: pointer;
}

.bottom-btn.clear {
  background-color: #ff6b6b;
  color: #ffffff;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 20px;
  max-width: 300px;
  width: 90%;
  overflow: hidden;
}

.modal-header {
  padding: 20px 20px 10px;
  text-align: center;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.modal-body {
  padding: 10px 20px 20px;
  text-align: center;
}

.modal-text {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

.modal-actions {
  display: flex;
  border-top: 1px solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 15px;
  border: none;
  background-color: #ffffff;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
}

.modal-btn.cancel {
  color: #999999;
  border-right: 1px solid #f0f0f0;
}

.modal-btn.confirm {
  color: #ff6b6b;
}

.modal-btn:active {
  background-color: #f8f8f8;
}
</style>
