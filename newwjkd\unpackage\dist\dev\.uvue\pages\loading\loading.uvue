
const __sfc__ = defineComponent({
  data() {
    return {
      loadingText: '加载中...',
      loadingDesc: '正在初始化应用',
      showProgress: false,
      progress: 0
    }
  },
  
  onLoad(options) {
    // 根据传入的参数设置加载文本
    if (options.type) {
      this.setLoadingType(options.type)
    }
    
    this.startLoading()
  },
  
  methods: {
    setLoadingType(type) {
      switch (type) {
        case 'upload':
          this.loadingText = '上传中...'
          this.loadingDesc = '正在上传文件，请稍候'
          this.showProgress = true
          break
        case 'download':
          this.loadingText = '下载中...'
          this.loadingDesc = '正在下载文件，请稍候'
          this.showProgress = true
          break
        case 'init':
          this.loadingText = '初始化中...'
          this.loadingDesc = '正在加载应用数据'
          break
        default:
          this.loadingText = '加载中...'
          this.loadingDesc = '请稍候...'
      }
    },
    
    startLoading() {
      // 模拟加载进度
      if (this.showProgress) {
        this.simulateProgress()
      } else {
        // 简单的加载延迟
        setTimeout(() => {
          this.completeLoading()
        }, 2000)
      }
    },
    
    simulateProgress() {
      const interval = setInterval(() => {
        if (this.progress < 90) {
          this.progress += Math.random() * 10
        } else if (this.progress < 100) {
          this.progress += 1
        } else {
          clearInterval(interval)
          this.completeLoading()
        }
      }, 200)
    },
    
    completeLoading() {
      // 加载完成，返回上一页或跳转到指定页面
      setTimeout(() => {
        uni.navigateBack({
          fail: () => {
            // 如果无法返回，则跳转到首页
            uni.reLaunch({
              url: '/pages/index/index'
            })
          }
        })
      }, 500)
    },
    
    // 外部调用更新进度
    updateProgress(progress, text) {
      this.progress = progress
      if (text) {
        this.loadingDesc = text
      }
    }
  }
})

export default __sfc__
function GenPagesLoadingLoadingRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
  return createElementVNode("view", utsMapOf({ class: "loading-container" }), [
    createElementVNode("view", utsMapOf({ class: "loading-content" }), [
      createElementVNode("view", utsMapOf({ class: "spinner-container" }), [
        createElementVNode("view", utsMapOf({ class: "spinner" }))
      ]),
      createElementVNode("text", utsMapOf({ class: "loading-text" }), toDisplayString(_ctx.loadingText), 1 /* TEXT */),
      createElementVNode("text", utsMapOf({ class: "loading-desc" }), toDisplayString(_ctx.loadingDesc), 1 /* TEXT */)
    ]),
    isTrue(_ctx.showProgress)
      ? createElementVNode("view", utsMapOf({
          key: 0,
          class: "progress-container"
        }), [
          createElementVNode("view", utsMapOf({ class: "progress-bar" }), [
            createElementVNode("view", utsMapOf({
              class: "progress-fill",
              style: normalizeStyle(utsMapOf({ width: _ctx.progress + '%' }))
            }), null, 4 /* STYLE */)
          ]),
          createElementVNode("text", utsMapOf({ class: "progress-text" }), toDisplayString(_ctx.progress) + "%", 1 /* TEXT */)
        ])
      : createCommentVNode("v-if", true)
  ])
}
const GenPagesLoadingLoadingStyles = [utsMapOf([["loading-container", padStyleMapOf(utsMapOf([["width", "100%"], ["backgroundColor", "#ffffff"], ["display", "flex"], ["flexDirection", "column"], ["justifyContent", "center"], ["alignItems", "center"], ["paddingTop", 40], ["paddingRight", 40], ["paddingBottom", 40], ["paddingLeft", 40]]))], ["loading-content", padStyleMapOf(utsMapOf([["display", "flex"], ["flexDirection", "column"], ["alignItems", "center"], ["marginBottom", 60]]))], ["spinner-container", padStyleMapOf(utsMapOf([["marginBottom", 30]]))], ["spinner", padStyleMapOf(utsMapOf([["width", 40], ["height", 40], ["borderWidth", 4], ["borderStyle", "solid"], ["borderColor", "#f3f3f3"], ["borderTopWidth", 4], ["borderTopStyle", "solid"], ["borderTopColor", "#1f999b"], ["animation", "spin 1s linear infinite"]]))], ["loading-text", padStyleMapOf(utsMapOf([["fontSize", 18], ["fontWeight", "bold"], ["color", "#1f999b"], ["marginBottom", 10]]))], ["loading-desc", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#666666"], ["textAlign", "center"], ["lineHeight", 1.5]]))], ["progress-container", padStyleMapOf(utsMapOf([["width", "80%"], ["maxWidth", 300], ["display", "flex"], ["flexDirection", "column"], ["alignItems", "center"]]))], ["progress-bar", padStyleMapOf(utsMapOf([["width", "100%"], ["height", 6], ["backgroundColor", "#f0f0f0"], ["borderRadius", 3], ["overflow", "hidden"], ["marginBottom", 10]]))], ["progress-fill", padStyleMapOf(utsMapOf([["height", "100%"], ["backgroundImage", "linear-gradient(90deg, #1f999b 0%, #16a085 100%)"], ["borderRadius", 3], ["transitionProperty", "width"], ["transitionDuration", "0.3s"], ["transitionTimingFunction", "ease"]]))], ["progress-text", padStyleMapOf(utsMapOf([["fontSize", 12], ["color", "#999999"]]))], ["@FONT-FACE", utsMapOf([["0", utsMapOf([])]])], ["@TRANSITION", utsMapOf([["progress-fill", utsMapOf([["property", "width"], ["duration", "0.3s"], ["timingFunction", "ease"]])]])]])]

//# sourceMappingURL=loading.uvue.map